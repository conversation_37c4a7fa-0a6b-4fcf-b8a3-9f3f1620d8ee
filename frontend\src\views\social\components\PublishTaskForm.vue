<template>
  <div class="publish-task-form">
    <div class="form-header">
      <h3>📋 第一步：选择内容来源</h3>
      <p class="form-description">选择平台、Core服务、账号和内容路径，这些信息将用于后续的发布配置</p>
    </div>

    <div class="form-content">
      <el-form :model="form" label-width="120px" class="task-form">
    <!-- 发布平台选择 -->
    <el-form-item label="发布平台" prop="platform" :rules="[
      { required: true, message: '请选择发布平台', trigger: 'change' }
    ]">
      <el-select
        v-model="form.platform"
        placeholder="请选择平台"
        @change="handlePlatformChange"
        :loading="loading.platforms"
      >
        <el-option
          v-for="platform in platforms"
          :key="platform.value"
          :label="platform.label"
          :value="platform.value"
        />
      </el-select>
    </el-form-item>

    <!-- Core服务选择 -->
    <el-form-item label="Core服务" prop="coreService" :rules="[
      { required: true, message: '请选择Core服务', trigger: 'change' }
    ]">
      <el-select
        v-model="form.coreService"
        placeholder="请选择Core服务"
        @change="handleCoreServiceChange"
        :loading="loading.coreServices"
      >
        <el-option
          v-for="service in coreServices"
          :key="service.id"
          :label="service.name"
          :value="service.id"
        />
      </el-select>
    </el-form-item>

    <!-- 发布账号选择 -->
    <el-form-item label="发布账号" prop="account" :rules="[
      { required: true, message: '请选择发布账号', trigger: 'change' }
    ]">
      <el-select
        v-model="form.account"
        placeholder="请选择账号"
        filterable
        :loading="loading.accounts"
        @change="handleAccountChange"
        popper-class="account-select-dropdown"
      >
        <el-option
          v-for="account in filteredAccounts"
          :key="account.id"
          :label="getAccountDisplayLabel(account)"
          :value="account.id"
        >
          <div class="account-option">
            <div class="account-main">
              <span class="account-display-name">{{ getAccountDisplayName(account) }}</span>
              <el-tag v-if="account.tags && account.tags.length > 0" size="small" type="primary" class="account-tag">
                {{ account.tags[0] }}
              </el-tag>
            </div>
            <div class="account-details">
              <span class="account-username">@{{ account.username || account.name }}</span>
              <span v-if="account.device_name && account.device_name !== getAccountDisplayName(account)" class="device-info">
                📱 {{ account.device_name }}
              </span>
            </div>
          </div>
        </el-option>
      </el-select>
    </el-form-item>

    <!-- 内容路径选择 -->
    <el-form-item label="内容路径" prop="contentPath" :rules="[
      { required: true, message: '请选择内容路径', trigger: 'blur' }
    ]">
      <div class="content-path-selector">
        <el-input
          v-model="form.contentPath"
          :placeholder="form.contentPath ? '已自动选择默认路径' : '请选择内容路径'"
          readonly
        >
          <template #append>
            <el-button
              :icon="Folder"
              @click="handlePathButtonClick"
              :loading="loading.paths"
              :type="form.contentPath ? 'default' : 'primary'"
            >
              {{ form.contentPath ? '修改' : '选择' }}
            </el-button>
          </template>
        </el-input>
      </div>
      <div v-if="form.contentPath" class="path-hint">
        <small>已自动选择默认路径，点击"修改"可更改</small>
        <el-button
          v-if="form.account"
          type="text"
          size="small"
          @click="previewFolderVideos"
          :loading="loading.previewVideos"
          class="preview-button"
        >
          🎬 预览视频文件
        </el-button>
      </div>
    </el-form-item>

    <!-- 发布策略配置 -->
    <el-form-item label="发布策略" prop="publishStrategy">
      <el-select v-model="form.publishStrategy" placeholder="请选择发布策略">
        <el-option label="立即发布" value="immediate" />
        <el-option label="定时发布" value="scheduled" />
        <el-option label="按频率发布" value="frequency" />
      </el-select>

      <!-- 定时发布选项 -->
      <div v-if="form.publishStrategy === 'scheduled'" class="strategy-options">
        <el-date-picker
          v-model="form.scheduleTime"
          type="datetime"
          placeholder="选择发布时间"
        />
      </div>

      <!-- 按频率发布选项 -->
      <div v-if="form.publishStrategy === 'frequency'" class="strategy-options">
        <el-form-item label="发布频率" class="nested-form-item">
          <el-input-number v-model="form.frequency" :min="1" :max="24" />
          <span class="frequency-unit">条/天</span>
        </el-form-item>

        <el-form-item label="发布时段" class="nested-form-item">
          <el-time-picker
            v-model="form.startTime"
            format="HH:mm"
            placeholder="开始时间"
          />
          <span class="time-separator">至</span>
          <el-time-picker
            v-model="form.endTime"
            format="HH:mm"
            placeholder="结束时间"
          />
        </el-form-item>
      </div>
    </el-form-item>

      </el-form>
    </div>

    <!-- 视频去重检查组件 -->
    <DuplicateVideoChecker
      ref="duplicateChecker"
      :show-statistics="false"
      @force-upload="handleForceUpload"
    />

    <!-- 内容选择信息 -->
    <div v-if="form.contentPath && form.account" class="content-info">
      <el-alert
        title="📁 内容处理说明"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="content-info-details">
            <p><strong>文件夹处理：</strong>系统将自动扫描文件夹下的所有视频文件，并为每个视频创建子任务</p>
            <p><strong>自动去重：</strong>基于文件内容hash值自动检测并过滤重复视频</p>
            <p><strong>支持格式：</strong>mp4, avi, mov, wmv, flv, mkv, webm, m4v等</p>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 允许重复上传选项 -->
    <div v-if="form.contentPath && form.account" class="force-upload-option">
      <el-checkbox v-model="forceUploadMode" size="large">
        <span class="force-upload-text">
          🔄 允许重复上传（忽略去重检查）
        </span>
      </el-checkbox>
      <div v-if="forceUploadMode" class="force-upload-warning">
        <el-text type="warning" size="small">
          ⚠️ 已启用重复上传，可能会上传相同内容的视频
        </el-text>
      </div>
    </div>

    <!-- 固定底部按钮区域 -->
    <div class="form-footer">
      <div class="button-group">
        <el-button type="primary" @click="submitForm" :loading="loading.submitting" size="large" class="primary-button">
          {{ props.existingTaskId ? '✅ 继续配置' : '🚀 创建任务' }}
        </el-button>
        <el-button @click="resetForm" class="secondary-button">
          🔄 重置
        </el-button>
      </div>
    </div>

  <!-- 内容路径选择对话框 -->
  <el-dialog
    v-model="showPathDialog"
    title="选择内容路径"
    width="70%"
    destroy-on-close
  >
    <div class="path-browser">
      <!-- 路径导航 -->
      <div class="path-navigation">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item v-if="currentPath === ''" @click="navigateToRoot">
            根目录
          </el-breadcrumb-item>
          <template v-else>
            <el-breadcrumb-item @click="navigateToRoot">
              根目录
            </el-breadcrumb-item>
            <template v-for="(part, index) in pathParts" :key="index">
              <el-breadcrumb-item @click="navigateToPath(index)">
                {{ part }}
              </el-breadcrumb-item>
            </template>
          </template>
        </el-breadcrumb>
      </div>

      <!-- 文件夹内容 -->
      <div class="path-content" v-loading="loading.pathContent">
        <el-table
          :data="pathContents"
          style="width: 100%"
          @row-click="handleRowClick"
          @selection-change="handleContentSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="名称" min-width="200">
            <template #default="{ row }">
              <div class="file-item">
                <el-icon v-if="row.is_directory"><Folder /></el-icon>
                <el-icon v-else><Document /></el-icon>
                <span>{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="size" label="大小" :formatter="formatSize" width="120" />
          <el-table-column prop="extension" label="类型" width="100" />
        </el-table>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="showPathDialog = false">取消</el-button>
        <el-button type="primary" @click="selectCurrentPath" :disabled="!currentPath || selectedContents.length === 0">
          选择内容 ({{ selectedContents.length }}个文件)
        </el-button>
      </span>
    </template>
  </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getSocialApps,
  getAccounts,
  createTask,
  getWorkflows,
  getCoreServices,
  getServiceAccounts,
  listFolderContents,
  listPlatformPaths,
  type FileInfo
} from '@/api/social'
import request from '@/utils/request'
import { Folder, Document } from '@element-plus/icons-vue'
import DuplicateVideoChecker from '@/components/DuplicateVideoChecker.vue'

// 表单数据
const form = reactive({
  platform: '',
  coreService: '',
  account: '',
  contentPath: '',
  publishStrategy: 'immediate', // 默认为立即发布
  scheduleTime: '', // 定时发布时间
  frequency: 3, // 默认每天发布3条
  startTime: new Date(2000, 0, 1, 9, 0), // 默认开始时间9:00
  endTime: new Date(2000, 0, 1, 18, 0) // 默认结束时间18:00
})

// 定义props，接收已存在的任务ID和任务数据
const props = defineProps<{
  existingTaskId?: string
  existingTaskData?: any
}>()

// 定义emit，支持传递任务ID
const emit = defineEmits<{
  (e: 'task-created', taskId: string): void
}>()

// 数据列表
const platforms = ref<any[]>([])
const coreServices = ref<any[]>([])
const accounts = ref<any[]>([])
const workflows = ref<any[]>([])

// 路径浏览相关
const showPathDialog = ref(false)
const currentPath = ref('')
const pathContents = ref<FileInfo[]>([])
const selectedContents = ref<FileInfo[]>([])
const rootPaths = ref<FileInfo[]>([])

// 视频去重相关
const duplicateChecker = ref()
const forceUploadMode = ref(false)

// 计算路径部分
const pathParts = computed(() => {
  if (!currentPath.value) return []
  // 对于Windows路径，需要特殊处理
  if (currentPath.value.includes(':\\')) {
    const drivePart = currentPath.value.split(':\\')[0] + ':\\'
    const restPath = currentPath.value.substring(drivePart.length)
    const parts = restPath.split('\\').filter(p => p)
    return [drivePart, ...parts]
  }
  return currentPath.value.split(/[\/\\]/).filter(p => p)
})

// 根据平台筛选账号
const filteredAccounts = computed(() => {
  if (!form.platform) return []
  return accounts.value.filter(account =>
    account.platform_id === form.platform
  )
})

// 获取账号显示名称
const getAccountDisplayName = (account: any) => {
  // 优先级：display_name > tags[0] > username > name > ID
  if (account.display_name && account.display_name !== account.username) {
    return account.display_name
  }
  if (account.tags && account.tags.length > 0) {
    return account.tags[0]
  }
  return account.username || account.name || `账号${account.id}`
}

// 获取账号在下拉框中的标签文本（用于搜索）
const getAccountDisplayLabel = (account: any) => {
  const displayName = getAccountDisplayName(account)
  const username = account.username || account.name
  const tag = account.tags && account.tags.length > 0 ? account.tags[0] : ''

  // 组合所有可能的搜索关键词
  const searchTerms = [displayName, username, tag].filter(Boolean).join(' ')
  return searchTerms
}

// 加载状态
const loading = reactive({
  platforms: false,
  coreServices: false,
  accounts: false,
  paths: false,
  pathContent: false,
  submitting: false,
  checkingDuplicates: false,
  previewVideos: false
})

// 初始化加载
onMounted(async () => {
  await fetchPlatforms()
  await fetchCoreServices()
  await fetchWorkflows()

  // 如果有已存在的任务数据，恢复表单状态
  if (props.existingTaskData) {
    await restoreFormData(props.existingTaskData)
  }
})

// 恢复表单数据
const restoreFormData = async (taskData: any) => {
  console.log('恢复任务表单数据:', taskData)

  // 恢复基本字段
  if (taskData.platform_id) {
    form.platform = taskData.platform_id
    await handlePlatformChange(taskData.platform_id)
  }

  if (taskData.core_service_id) {
    form.coreService = taskData.core_service_id
    await handleCoreServiceChange(taskData.core_service_id)
  }

  if (taskData.account_id) {
    form.account = taskData.account_id
    await handleAccountChange(taskData.account_id)
  }

  if (taskData.content_path) {
    form.contentPath = taskData.content_path
  }

  if (taskData.publish_strategy) {
    form.publishStrategy = taskData.publish_strategy
  }

  if (taskData.schedule_time) {
    form.scheduleTime = taskData.schedule_time
  }

  if (taskData.frequency) {
    form.frequency = taskData.frequency
  }

  console.log('表单数据恢复完成:', form)
}

// 获取发布平台列表
const fetchPlatforms = async () => {
  try {
    loading.platforms = true
    const res = await getSocialApps()

    if (res && res.data && res.data.length > 0) {
      // 打印原始平台数据，帮助调试
      console.log('原始平台数据:', JSON.stringify(res.data, null, 2))

      platforms.value = res.data.map(p => {
        // 打印每个平台的详细信息，帮助调试
        console.log('平台详情:', JSON.stringify(p, null, 2))

        // 尝试获取正确的ID字段
        // 在MongoDB中，_id是ObjectId，而id是字符串
        const platformId = p.id || (p._id ? p._id.toString() : null)

        console.log(`平台 ${p.name} 的ID字段:`, {
          _id: p._id,
          id: p.id,
          platformId
        })

        return {
          value: p._id || p.id, // 使用 _id 作为值，这是MongoDB的ID，用于表单选择
          id: platformId,       // 使用正确的ID字段用于构建路径
          _id: p._id,           // 保存MongoDB的_id
          label: p.name,
          icon: p.icon,
          // 保存原始数据，以便调试
          raw: p
        }
      })

      // 打印平台信息，帮助调试
      console.log('平台信息详情:', res.data)
      console.log('处理后的平台列表:', platforms.value)
      console.log('获取到平台列表:', platforms.value)
    } else {
      console.warn('未获取到平台列表或列表为空')
      ElMessage.warning('未找到可用的平台，请先在平台管理中添加平台')
      platforms.value = []
    }
  } catch (error) {
    console.error('获取平台列表失败:', error)
    ElMessage.error('获取平台列表失败，请检查网络连接和后端服务状态')
    platforms.value = []
  } finally {
    loading.platforms = false
  }
}

// 获取Core服务列表
const fetchCoreServices = async () => {
  try {
    loading.coreServices = true
    const res = await getCoreServices()

    if (res && res.data && res.data.length > 0) {
      coreServices.value = res.data.map(s => ({
        id: s.id,
        name: s.name || s.id,
        status: s.status || 'unknown'
      }))
      console.log('获取到Core服务列表:', coreServices.value)
    } else {
      console.warn('未获取到Core服务列表或列表为空')
      ElMessage.warning('未找到可用的Core服务，请确保Core服务已正确注册到Consul')
      coreServices.value = []
    }
  } catch (error) {
    console.error('获取Core服务列表失败:', error)
    ElMessage.error('获取Core服务列表失败，请检查网络连接和后端服务状态')
    coreServices.value = []
  } finally {
    loading.coreServices = false
  }
}

// 获取账号列表
const fetchAccounts = async (platformId: string, coreServiceId: string) => {
  if (!platformId || !coreServiceId) return

  try {
    loading.accounts = true
    console.log(`开始获取账号列表: platformId=${platformId}, coreServiceId=${coreServiceId}`)

    const res = await getServiceAccounts(platformId, coreServiceId)
    console.log('获取账号API响应:', res)

    // 检查响应格式，处理嵌套的data结构
    const accountsData = res.data?.data || [];
    console.log('解析后的账号数据:', accountsData)

    if (accountsData && accountsData.length > 0) {
      // 打印原始数据，帮助调试
      console.log('原始账号数据:', accountsData)

      accounts.value = accountsData.map(a => {
        // 打印每个账号的详细信息，帮助调试
        console.log('账号详情:', JSON.stringify(a, null, 2))

        // 尝试获取正确的显示名称和设备名称
        // 优先使用display_name作为显示名称
        const displayName = a.display_name || a.username || a.name || `账号${a._id || a.id}`
        // 如果有设备ID，应该使用设备的name字段
        // 由于我们没有设备信息，暂时使用账号显示名称
        const deviceName = displayName

        console.log(`账号 ${a._id} 的显示名称:`, displayName)
        console.log(`账号 ${a._id} 的设备名称:`, deviceName)

        return {
          id: a._id || a.id,
          // 优先使用username作为name，这是用于构建路径的关键字段
          name: a.username || a.display_name || a.name || `账号${a._id || a.id}`,
          username: a.username,
          // 显示名称，优先使用display_name
          display_name: displayName,
          platform_id: a.platform_id,
          device_id: a.device_id,
          // 设备名称，默认使用账号显示名称
          device_name: deviceName,
          // 标签信息
          tags: a.tags || [],
          // 状态信息
          status: a.status || 'active',
          // 保存原始数据，以便调试
          raw: a
        }
      })
      console.log(`获取到${accounts.value.length}个账号:`, accounts.value)
    } else {
      console.warn('未获取到账号或账号列表为空')
      console.log('API响应详情:', res)
      ElMessage.warning('未找到可用的账号，请先在账号管理中添加账号')
      accounts.value = []
    }
  } catch (error) {
    console.error('获取账号列表失败:', error)
    console.error('错误详情:', error.response || error.message || error)
    ElMessage.error('获取账号列表失败，请检查网络连接和后端服务状态')
    accounts.value = []
  } finally {
    loading.accounts = false
  }
}

// 获取工作流列表
const fetchWorkflows = async () => {
  try {
    console.log('开始获取工作流...')
    const res = await getWorkflows()
    console.log('工作流API响应:', res)

    if (res && res.data) {
      workflows.value = res.data.map(w => ({
        value: w.id,
        label: w.name,
        description: w.description || ''
      }))
    } else {
      console.warn('工作流API返回空数据')
      workflows.value = []
    }
  } catch (error) {
    console.error('获取工作流失败', error)
    // 设置为空数组而不是抛出错误，避免阻断其他功能
    workflows.value = []

    // 如果是404错误，可能是API不存在
    if (error.response && error.response.status === 404) {
      console.log('工作流API不存在')
      ElMessage.warning('工作流API不可用，请确保后端服务已正确配置')
    }
  }
}

// 获取平台根路径
const fetchPlatformRootPaths = async () => {
  if (!form.platform || !form.coreService || !form.account) {
    ElMessage.warning('请先选择平台、Core服务和账号')
    return
  }

  try {
    loading.paths = true

    console.log(`获取发布路径: platformId=${form.platform}, coreServiceId=${form.coreService}, accountId=${form.account}`)

    // 从API获取发布路径
    const res = await listPlatformPaths(form.platform, form.coreService, form.account)
    console.log('获取发布路径API响应:', res)

    if (!res || !res.data || !res.data.paths || res.data.paths.length === 0) {
      console.warn('未获取到发布路径或路径列表为空')
      ElMessage.warning('未找到可用的发布路径，请确保Core服务配置正确')
      pathContents.value = []
      return
    }

    // 获取基础路径和路径列表
    const basePath = res.data.base_path
    const paths = res.data.paths

    console.log(`获取到${paths.length}个路径:`, paths)
    console.log(`基础路径: ${basePath}`)

    // 查找内容目录路径和设备目录路径
    const contentPath = paths.find(p => p.name === '内容目录')
    const devicePath = paths.find(p => p.name === '设备目录')

    console.log('内容目录路径:', contentPath)
    console.log('设备目录路径:', devicePath)

    // 设置根路径
    rootPaths.value = paths.map(p => ({
      name: p.name || p.path.split('\\').pop() || '内容目录',
      path: p.path,
      is_directory: true,
      size: 0
    }))

    // 如果有路径，按优先级选择：内容目录 > 设备目录 > 第一个路径
    if (rootPaths.value.length > 0) {
      // 获取账号和设备名称，用于构建路径
      const account = accounts.value.find(a => a.id === form.account)

      // 优先使用账号的display_name字段
      const displayName = account && account.raw ?
        (account.raw.display_name || account.raw.username || account.name) :
        (account ? account.name : form.account)

      // 设备名称，如果有设备信息则使用设备名称，否则使用账号显示名称
      const deviceName = account ? account.device_name : displayName

      console.log('账号显示名称:', displayName)

      // 获取平台信息
      const platformObj = platforms.value.find(p => p.value === form.platform)

      // 使用平台的platform字段作为目录名，这是平台的标识符
      const platformCode = platformObj && platformObj.raw ? platformObj.raw.platform : null
      // 如果没有platform字段，则使用id字段
      const platformId = platformCode || (platformObj ? platformObj.id : form.platform)
      const platformName = platformObj ? platformObj.label : form.platform

      console.log('使用的平台标识符:', platformId, '(来自platform字段:', platformCode, ')')

      console.log('构建路径使用的平台信息:', {
        platformId,
        platformName,
        platformCode,
        originalValue: form.platform,
        platformObj
      })
      console.log('构建路径使用的账号信息:', {
        displayName,
        deviceName,
        originalValue: form.account,
        account
      })

      if (contentPath) {
        console.log('找到内容目录路径，使用内容目录路径:', contentPath.path)
        currentPath.value = contentPath.path

        // 自动设置内容路径
        form.contentPath = contentPath.path
      } else if (devicePath) {
        console.log('找到设备目录路径，使用设备目录路径:', devicePath.path)
        currentPath.value = devicePath.path

        // 自动设置内容路径
        form.contentPath = devicePath.path
      } else {
        // 如果没有找到内容目录或设备目录路径，尝试构建一个标准路径
        const basePath = rootPaths.value[0].path
        console.log('未找到内容目录或设备目录路径，使用第一个路径:', basePath)

        // 构建标准路径：根目录/平台ID/设备name/
        // 注意：这里我们不能真正创建目录，但可以构建一个标准路径
        const standardPath = `${basePath}\\${platformId}\\${deviceName}`
        console.log('构建标准路径:', standardPath)

        // 使用标准路径作为当前路径和内容路径
        currentPath.value = basePath

        // 自动设置内容路径为标准路径
        form.contentPath = standardPath

        // 显示提示信息
        const displayPath = standardPath.split('\\').slice(-2).join('\\')
        ElMessage.info(`系统将使用标准路径: ...\\${displayPath}`)
      }

      // 加载路径内容
      try {
        await loadPathContents(currentPath.value)
        console.log(`成功加载路径内容: ${currentPath.value}`)
      } catch (pathError) {
        console.error(`加载路径内容失败: ${pathError}`)
        ElMessage.warning(`无法加载路径 ${currentPath.value} 的内容，请确保该路径存在`)
        pathContents.value = []
      }
    } else {
      // 如果没有路径，清空内容
      pathContents.value = []
      ElMessage.info('未找到可用的内容路径，请联系管理员配置Core服务')
    }
  } catch (error) {
    console.error('获取平台路径失败:', error)
    console.error('错误详情:', error.response || error.message || error)
    ElMessage.error('获取平台路径失败，请检查网络连接和后端服务状态')
    pathContents.value = []
  } finally {
    loading.paths = false
  }
}

// 加载路径内容
const loadPathContents = async (path: string) => {
  try {
    loading.pathContent = true
    const res = await listFolderContents(path)
    pathContents.value = res.data.files || []
    currentPath.value = path
  } catch (error) {
    console.error('加载文件夹内容失败:', error)
    ElMessage.error('加载文件夹内容失败')
  } finally {
    loading.pathContent = false
  }
}

// 导航到根目录
const navigateToRoot = async () => {
  if (rootPaths.value.length > 0) {
    currentPath.value = rootPaths.value[0].path
    await loadPathContents(currentPath.value)
  } else {
    await fetchPlatformRootPaths()
  }
}

// 导航到指定路径
const navigateToPath = async (index: number) => {
  if (index < 0) {
    await navigateToRoot()
    return
  }

  // 重建路径
  const newPath = pathParts.value.slice(0, index + 1).join('\\')
  await loadPathContents(newPath)
}

// 处理行点击
const handleRowClick = async (row: FileInfo, column: any) => {
  // 如果点击的是选择框列，不进行导航
  if (column.type === 'selection') return

  if (row.is_directory) {
    await loadPathContents(row.path)
  }
}

// 处理内容选择变化
const handleContentSelectionChange = (selection: FileInfo[]) => {
  selectedContents.value = selection
}

// 选择当前路径
const selectCurrentPath = () => {
  // 如果没有选择任何文件，但有当前路径，则使用当前路径
  if (selectedContents.value.length === 0) {
    if (currentPath.value) {
      console.log(`未选择文件，使用当前路径: ${currentPath.value}`)
      form.contentPath = currentPath.value
      showPathDialog.value = false

      // 显示成功消息，包含路径信息
      const pathParts = currentPath.value.split('\\')
      const displayPath = pathParts.length > 2 ?
        `...\\${pathParts.slice(-2).join('\\')}` :
        currentPath.value

      ElMessage.success(`已选择路径: ${displayPath}`)
      return
    }

    ElMessage.warning('请选择至少一个文件或文件夹')
    return
  }

  // 将选中的文件路径合并为一个字符串
  form.contentPath = selectedContents.value.map(file => file.path).join(';')
  console.log(`已选择路径: ${form.contentPath}`)
  showPathDialog.value = false

  // 显示成功消息，包含选择的文件数量
  if (selectedContents.value.length === 1) {
    const pathParts = selectedContents.value[0].path.split('\\')
    const displayPath = pathParts.length > 2 ?
      `...\\${pathParts.slice(-2).join('\\')}` :
      selectedContents.value[0].path

    ElMessage.success(`已选择路径: ${displayPath}`)
  } else {
    ElMessage.success(`已选择 ${selectedContents.value.length} 个文件/文件夹`)
  }
}

// 格式化文件大小
const formatSize = (row: any, column: any, bytes: number) => {
  if (!bytes || bytes <= 0) return '未知'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化时间为HH:MM格式
const formatTime = (date: Date) => {
  if (!date) return ''
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

// 处理平台变化
const handlePlatformChange = async () => {
  // 清空依赖字段
  form.coreService = ''
  form.account = ''
  form.contentPath = ''
  accounts.value = []
}

// 处理Core服务变化
const handleCoreServiceChange = async () => {
  // 清空依赖字段
  form.account = ''
  form.contentPath = ''

  // 获取账号列表
  if (form.platform && form.coreService) {
    await fetchAccounts(form.platform, form.coreService)
  }
}

// 处理路径按钮点击
const handlePathButtonClick = async () => {
  console.log('点击路径按钮，当前内容路径:', form.contentPath)

  // 如果已经有内容路径，需要导航到该路径
  if (form.contentPath) {
    console.log('已有内容路径，尝试导航到该路径:', form.contentPath)

    try {
      // 如果是标准路径（根目录/平台ID/设备name/），需要特殊处理
      if (form.contentPath.includes('\\')) {
        // 获取路径的各个部分
        const pathParts = form.contentPath.split('\\')

        // 如果路径至少有3个部分（根目录/平台ID/设备name/），尝试导航到该路径
        if (pathParts.length >= 3) {
          // 设置当前路径为标准路径
          currentPath.value = form.contentPath

          // 尝试加载路径内容
          try {
            await loadPathContents(currentPath.value)
            console.log(`成功加载路径内容: ${currentPath.value}`)
          } catch (pathError) {
            console.error(`加载路径内容失败: ${pathError}`)

            // 如果加载失败，尝试加载父目录
            const parentPath = pathParts.slice(0, -1).join('\\')
            console.log(`尝试加载父目录: ${parentPath}`)

            try {
              currentPath.value = parentPath
              await loadPathContents(parentPath)
              console.log(`成功加载父目录内容: ${parentPath}`)
            } catch (parentError) {
              console.error(`加载父目录内容失败: ${parentError}`)

              // 如果父目录也加载失败，尝试加载根目录
              const rootPath = pathParts[0]
              console.log(`尝试加载根目录: ${rootPath}`)

              try {
                currentPath.value = rootPath
                await loadPathContents(rootPath)
                console.log(`成功加载根目录内容: ${rootPath}`)
              } catch (rootError) {
                console.error(`加载根目录内容失败: ${rootError}`)
                ElMessage.warning(`无法加载路径内容，请确保路径存在`)

                // 如果根目录也加载失败，重新获取平台路径
                await fetchPlatformRootPaths()
              }
            }
          }
        } else {
          // 如果路径不足3个部分，直接设置当前路径为内容路径
          currentPath.value = form.contentPath
          await loadPathContents(currentPath.value)
        }
      } else {
        // 如果不是标准路径，直接设置当前路径为内容路径
        currentPath.value = form.contentPath
        await loadPathContents(currentPath.value)
      }
    } catch (error) {
      console.error('导航到路径失败:', error)
      ElMessage.warning('无法导航到当前路径，将重新获取路径')

      // 如果导航失败，重新获取平台路径
      await fetchPlatformRootPaths()
    }
  }

  // 打开路径选择对话框
  showPathDialog.value = true
}

// 处理账号变化
const handleAccountChange = async () => {
  // 清空内容路径
  form.contentPath = ''

  console.log('账号变化，当前选择：', {
    platform: form.platform,
    coreService: form.coreService,
    account: form.account
  })

  // 如果所有必要字段都已选择，获取平台路径
  if (form.platform && form.coreService && form.account) {
    console.log('所有必要字段已选择，开始获取平台路径')
    try {
      loading.paths = true
      await fetchPlatformRootPaths()

      // 不需要自动打开路径选择对话框，因为我们已经自动设置了内容路径
      console.log('已自动设置内容路径:', form.contentPath)

      // 显示成功消息，包含路径信息
      if (form.contentPath) {
        const pathParts = form.contentPath.split('\\')
        const displayPath = pathParts.length > 2 ?
          `...\\${pathParts.slice(-2).join('\\')}` :
          form.contentPath

        ElMessage.success(`已自动设置内容路径: ${displayPath}`)
      } else {
        // 如果没有自动设置内容路径，则打开路径选择对话框
        console.log('未能自动设置内容路径，打开路径选择对话框')
        showPathDialog.value = true
      }
    } catch (error) {
      console.error('获取平台路径失败:', error)
      ElMessage.error('获取平台路径失败，请稍后重试')
    } finally {
      loading.paths = false
    }
  }
}

// 预览文件夹中的视频文件
const previewFolderVideos = async () => {
  if (!form.contentPath || !form.account) {
    ElMessage.warning('请先选择内容路径和账号')
    return
  }

  try {
    loading.previewVideos = true

    const response = await request.post('/api/v1/social/youtube/preview-folder-videos', {
      folderPath: form.contentPath,
      accountId: form.account
    })

    const data = response.data

    if (data.total_count === 0) {
      ElMessage.info('文件夹中未找到视频文件')
      return
    }

    // 统计重复文件
    const duplicateCount = data.duplicate_info?.filter(item => item.is_duplicate).length || 0
    const uniqueCount = data.total_count - duplicateCount

    // 构建预览内容
    let previewContent = `📁 文件夹：${form.contentPath}\n\n`
    previewContent += `📊 统计信息：\n`
    previewContent += `• 总视频文件：${data.total_count} 个\n`
    previewContent += `• 新视频文件：${uniqueCount} 个\n`
    previewContent += `• 重复视频文件：${duplicateCount} 个\n\n`

    if (data.video_files.length > 0) {
      previewContent += `📋 视频文件列表：\n`
      data.video_files.slice(0, 10).forEach((file, index) => {
        const duplicateInfo = data.duplicate_info?.find(item => item.file === file)
        const status = duplicateInfo?.is_duplicate ? '🔄 重复' : '✨ 新文件'
        previewContent += `${index + 1}. ${file} (${status})\n`
      })

      if (data.video_files.length > 10) {
        previewContent += `... 还有 ${data.video_files.length - 10} 个文件\n`
      }
    }

    ElMessageBox.alert(previewContent, '视频文件预览', {
      confirmButtonText: '知道了',
      type: 'info'
    })

  } catch (error) {
    console.error('预览视频文件失败:', error)
    ElMessage.error('预览视频文件失败，请重试')
  } finally {
    loading.previewVideos = false
  }
}

// 强制上传处理（从重复检查对话框触发）
const handleForceUpload = () => {
  forceUploadMode.value = true
  ElMessage.info('已启用强制上传模式，将忽略重复检查')
  submitForm()
}

// 判断是否为视频文件
const isVideoFile = (filename: string) => {
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', 'm4v']
  const ext = filename.split('.').pop()?.toLowerCase()
  return ext ? videoExtensions.includes(ext) : false
}

// 提交表单
const submitForm = async () => {
  if (!form.platform || !form.coreService || !form.account || !form.contentPath) {
    ElMessage.warning('请填写所有必填字段')
    return
  }

  // 自动去重将在后端处理，前端只需要传递force_upload标志

  // 如果已存在任务ID，直接进入下一步，不创建新任务
  if (props.existingTaskId) {
    console.log('使用已存在的任务ID:', props.existingTaskId)

    // 构建当前的任务数据，用于更新缓存
    const currentTaskData: any = {
      platform_id: form.platform,
      core_service_id: form.coreService,
      account_id: form.account,
      content_path: form.contentPath,
      publish_strategy: form.publishStrategy
    }

    // 根据发布策略添加相应的字段
    if (form.publishStrategy === 'scheduled') {
      currentTaskData.schedule_type = 'scheduled'
      if (form.scheduleTime instanceof Date) {
        currentTaskData.schedule_time = form.scheduleTime.toISOString()
      } else {
        currentTaskData.schedule_time = form.scheduleTime || ''
      }
    } else if (form.publishStrategy === 'frequency') {
      currentTaskData.schedule_type = 'frequency'
      currentTaskData.frequency = form.frequency
      currentTaskData.start_time = form.startTime ? formatTime(form.startTime) : '09:00'
      currentTaskData.end_time = form.endTime ? formatTime(form.endTime) : '18:00'
    } else {
      currentTaskData.schedule_type = 'immediate'
      currentTaskData.schedule_time = ''
    }

    console.log('当前任务数据:', currentTaskData)
    emit('task-created', props.existingTaskId, currentTaskData)
    return
  }

  try {
    loading.submitting = true

    // 构建请求数据
    const taskData: any = {
      platform_id: form.platform,
      core_service_id: form.coreService,
      account_id: form.account,
      content_path: form.contentPath,
      publish_strategy: form.publishStrategy,
      force_upload: forceUploadMode.value  // 添加强制上传标志
    }

    // 处理选中的文件/文件夹
    if (selectedContents.value.length > 0) {
      // 过滤出视频文件
      const videoFiles = selectedContents.value.filter(item =>
        !item.is_directory && isVideoFile(item.name)
      )

      // 过滤出文件夹
      const folders = selectedContents.value.filter(item => item.is_directory)

      if (videoFiles.length > 0) {
        // 如果选择了具体的视频文件
        taskData.selected_files = videoFiles.map(file => file.name)  // 🔧 修复：使用下划线命名匹配通用API
        console.log(`选择了 ${videoFiles.length} 个视频文件:`, taskData.selected_files)
      } else if (folders.length > 0) {
        // 如果只选择了文件夹，让后端自动扫描
        console.log(`选择了 ${folders.length} 个文件夹，后端将自动扫描视频文件`)
        // 不设置 selected_files，让后端自动扫描
      } else {
        // 如果选择的都不是视频文件或文件夹
        ElMessage.warning('请选择视频文件或包含视频的文件夹')
        return
      }
    }

    // 根据发布策略添加相应的字段
    if (form.publishStrategy === 'scheduled') {
      // 定时发布
      taskData.schedule_type = 'scheduled'
      // 如果scheduleTime是Date对象，则转换为ISO字符串
      if (form.scheduleTime instanceof Date) {
        taskData.schedule_time = form.scheduleTime.toISOString()
      } else {
        taskData.schedule_time = form.scheduleTime || ''
      }
    } else if (form.publishStrategy === 'frequency') {
      // 按频率发布
      taskData.schedule_type = 'frequency'
      taskData.frequency = form.frequency
      taskData.start_time = form.startTime ? formatTime(form.startTime) : '09:00'
      taskData.end_time = form.endTime ? formatTime(form.endTime) : '18:00'
    } else {
      // 立即发布
      taskData.schedule_type = 'immediate'
      taskData.schedule_time = ''
    }

    const response = await createTask(taskData)

    // 处理去重结果
    if (response.data?.status === 'all_duplicates_filtered') {
      // 所有文件都被过滤
      ElMessage.warning({
        message: response.data.duplicate_info.message,
        duration: 5000
      })

      // 显示详细的过滤信息
      const duplicateFiles = response.data.duplicate_info.duplicate_files
      if (duplicateFiles && duplicateFiles.length > 0) {
        const fileList = duplicateFiles.map(f => f.file).join(', ')
        ElMessageBox.alert(
          `以下视频文件已上传过，已自动过滤：\n${fileList}\n\n如需重新上传，请勾选"允许重复上传"选项。`,
          '自动去重结果',
          {
            confirmButtonText: '知道了',
            type: 'info'
          }
        )
      }
      return
    }

    // 检查是否有部分文件被过滤
    if (response.data?.duplicate_info?.filtered_count > 0) {
      ElMessage.info({
        message: `已自动过滤 ${response.data.duplicate_info.filtered_count} 个重复视频，继续处理其余文件`,
        duration: 4000
      })
    }

    ElMessage.success('任务创建成功')

    // 获取任务ID
    const taskId = response.data?.task_id || 'temp-task-id'
    console.log('创建的任务ID:', taskId)

    // 发出任务创建成功事件，并传递任务ID和任务数据
    emit('task-created', taskId, taskData)

    // 重置表单
    resetForm()
  } catch (error) {
    ElMessage.error('任务创建失败')
    console.error(error)
  } finally {
    loading.submitting = false
  }
}

// 重置表单
const resetForm = () => {
  form.platform = ''
  form.coreService = ''
  form.account = ''
  form.contentPath = ''
  form.publishStrategy = 'immediate'
  forceUploadMode.value = false  // 重置强制上传模式
  form.scheduleTime = ''
  form.frequency = 3
  form.startTime = new Date(2000, 0, 1, 9, 0)
  form.endTime = new Date(2000, 0, 1, 18, 0)
  selectedContents.value = []
}
</script>

<style scoped>
.publish-task-form {
  height: 100%;
  display: grid;
  grid-template-rows: auto 1fr auto; /* 头部 内容 底部 */
  overflow: hidden;
}

.form-header {
  padding: 20px 20px 10px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  grid-row: 1;
}

.form-header h3 {
  margin: 0 0 8px 0;
  color: #409EFF;
  font-size: 1.5rem;
}

.form-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-content {
  grid-row: 2;
  overflow-y: auto;
  padding: 20px;
  min-height: 0;
}

.task-form {
  max-width: 100%;
}

.form-footer {
  padding: 15px 20px;
  background-color: #fafafa;
  border-top: 1px solid #ebeef5;
  grid-row: 3;
}

.content-path-selector {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.path-hint {
  width: 100%;
  text-align: left;
  margin-top: 5px;
  color: #67c23a;
  font-size: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.preview-button {
  margin-left: 10px;
  color: #409eff;
  font-size: 12px;
}

.preview-button:hover {
  color: #66b1ff;
}

.path-browser {
  display: flex;
  flex-direction: column;
  height: 400px;
}

.path-navigation {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.path-content {
  flex: 1;
  overflow: auto;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.el-breadcrumb-item {
  cursor: pointer;
}

.workflow-option {
  display: flex;
  flex-direction: column;
}

.workflow-option small {
  color: #909399;
  font-size: 12px;
}

/* 发布策略样式 */
.strategy-options {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.nested-form-item {
  margin-bottom: 10px;
}

.frequency-unit {
  margin-left: 5px;
  color: #606266;
}

.time-separator {
  margin: 0 10px;
  color: #606266;
}

/* 按钮样式 */
.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.primary-button {
  font-weight: bold;
  padding: 12px 30px;
  border-radius: 6px;
}

.secondary-button {
  padding: 10px 20px;
  border-radius: 6px;
}

/* 内容信息相关样式 */
.content-info {
  margin: 20px 0;
}

.content-info-details {
  line-height: 1.6;
}

.content-info-details p {
  margin: 8px 0;
}

.content-info-details strong {
  color: #409eff;
  font-weight: 600;
}

.force-upload-option {
  margin: 15px 0;
  padding: 15px;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
}

.force-upload-text {
  font-weight: 500;
  color: #92400e;
}

.force-upload-warning {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
  border-radius: 4px;
}

/* 账号选择下拉框样式 */
.account-option {
  padding: 8px 0;
}

.account-main {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.account-display-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.account-tag {
  margin-left: 8px;
}

.account-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.account-username {
  color: #606266;
}

.device-info {
  color: #909399;
  font-size: 11px;
}

/* 下拉框样式 */
:deep(.account-select-dropdown) {
  min-width: 300px !important;
}

:deep(.account-select-dropdown .el-select-dropdown__item) {
  height: auto !important;
  padding: 8px 20px !important;
  line-height: 1.4 !important;
}

:deep(.account-select-dropdown .el-select-dropdown__item.hover) {
  background-color: #f5f7fa;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-header {
    padding: 15px 15px 10px 15px;
  }

  .form-content {
    padding: 15px;
  }

  .form-footer {
    padding: 15px;
  }

  .button-group {
    flex-direction: column;
    gap: 10px;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
  }

  /* 移动端账号选择样式 */
  :deep(.account-select-dropdown) {
    min-width: 280px !important;
  }

  .account-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
</style>
