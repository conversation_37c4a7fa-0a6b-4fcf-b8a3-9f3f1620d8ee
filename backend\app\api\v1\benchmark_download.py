"""
对标账号下载API - 基于工作流的下载器
参考YouTube上传器架构设计
"""

import os
import uuid
import logging
import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from pydantic import BaseModel, Field
from bson import ObjectId

from app.core.security import get_current_user
from app.core.schemas.social_repository import SocialDatabaseService
from app.api.social import get_social_service

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/benchmark",
    tags=["benchmark_download"]
    # 临时移除认证依赖进行测试
    # dependencies=[Depends(get_current_user)]
)

# 测试端点（不需要认证）
@router.get("/test")
async def test_endpoint():
    """测试端点"""
    return {"message": "对标账号下载API正常工作", "timestamp": datetime.datetime.now()}


# 定义数据模型
class DownloadConfig(BaseModel):
    """下载配置"""
    max_videos: int = Field(default=50, ge=1, le=1000, description="最大下载视频数量")
    video_quality: str = Field(default="high", description="视频质量: high/medium/low")
    include_metadata: bool = Field(default=True, description="是否包含元数据")
    skip_existing: bool = Field(default=True, description="是否跳过已存在的文件")
    date_range: Optional[Dict[str, str]] = Field(default=None, description="日期范围")
    content_types: List[str] = Field(default=["video"], description="内容类型")
    min_views: Optional[int] = Field(default=None, description="最小播放量")
    min_likes: Optional[int] = Field(default=None, description="最小点赞数")
    keywords: Optional[List[str]] = Field(default=None, description="关键词过滤")
    download_mode: str = Field(default="auto", description="下载模式: auto/manual")
    naming_rule: str = Field(default="timestamp", description="命名规则")

class BenchmarkDownloadTaskCreate(BaseModel):
    """创建对标账号下载任务"""
    our_account_id: str = Field(..., description="我们的账号ID")
    our_account_name: str = Field(..., description="我们的账号名称")
    benchmark_account_id: str = Field(..., description="对标账号ID")
    benchmark_account_name: str = Field(..., description="对标账号名称")
    benchmark_account_url: str = Field(..., description="对标账号URL")
    platform: str = Field(..., description="平台名称")
    download_path: str = Field(..., description="下载路径")
    download_config: DownloadConfig = Field(..., description="下载配置")

class BatchBenchmarkDownloadTaskCreate(BaseModel):
    """批量创建对标账号下载任务"""
    tasks: List[BenchmarkDownloadTaskCreate] = Field(..., description="批量下载任务列表")

class DownloadTaskResponse(BaseModel):
    """下载任务响应"""
    task_id: str
    status: str
    estimated_time: int = 3600
    download_path: str
    created_at: datetime.datetime

class BatchDownloadTaskResponse(BaseModel):
    """批量下载任务响应"""
    batch_id: str
    task_ids: List[str]
    total_tasks: int
    status: str
    estimated_time: int
    created_at: datetime.datetime

class DownloadTaskStatus(BaseModel):
    """下载任务状态"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="状态")
    progress: int = Field(default=0, description="进度百分比")
    downloaded_count: int = Field(default=0, description="已下载数量")
    total_count: int = Field(default=0, description="总数量")
    error_message: Optional[str] = Field(None, description="错误信息")
    updated_at: Optional[datetime.datetime] = Field(None, description="更新时间")
    logs: List[str] = Field(default=[], description="日志信息")


# 创建单个下载任务
@router.post("/download/tasks", response_model=DownloadTaskResponse)
async def create_download_task(
    task: BenchmarkDownloadTaskCreate,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    创建对标账号下载任务

    - **our_account_id**: 我们的账号ID
    - **benchmark_account_id**: 对标账号ID
    - **download_config**: 下载配置
    """
    logger.info(f"创建下载任务: {task.benchmark_account_name} -> {task.download_path}")

    try:
        # 验证我们的账号是否存在
        our_account = db_service.get_account_by_id(task.our_account_id)
        if not our_account:
            raise HTTPException(status_code=404, detail="我们的账号不存在")

        # 验证对标账号是否存在
        logger.info(f"查询对标账号: {task.benchmark_account_id} (类型: {type(task.benchmark_account_id)})")

        benchmark_account = None

        try:
            # 确保ID是字符串格式
            account_id_str = str(task.benchmark_account_id)
            logger.info(f"转换后的账号ID: {account_id_str}")

            # 检查是否是有效的ObjectId格式
            if ObjectId.is_valid(account_id_str):
                logger.info(f"ID格式有效，尝试ObjectId查询")
                benchmark_account = db_service.db.benchmark_accounts.find_one({"_id": ObjectId(account_id_str)})
                logger.info(f"ObjectId查询结果: {benchmark_account is not None}")

                if benchmark_account:
                    logger.info(f"找到对标账号: {benchmark_account.get('account_name', 'Unknown')}")
            else:
                logger.warning(f"ID格式无效: {account_id_str}")

        except Exception as e:
            logger.error(f"查询对标账号时发生异常: {str(e)}", exc_info=True)

        if not benchmark_account:
            # 记录调试信息
            total_count = db_service.db.benchmark_accounts.count_documents({})
            logger.error(f"对标账号不存在。数据库中共有 {total_count} 个对标账号")

            # 列出前几个对标账号用于调试
            sample_accounts = list(db_service.db.benchmark_accounts.find({}).limit(3))
            for i, acc in enumerate(sample_accounts):
                logger.error(f"示例账号 {i+1}: _id={acc.get('_id')} (类型: {type(acc.get('_id'))}), name='{acc.get('account_name', 'Unknown')}'")

            raise HTTPException(status_code=404, detail=f"对标账号不存在: {task.benchmark_account_id}")

        # 🔧 查找对应的平台ID和账号ID（Core服务需要）
        platform_id = ""
        account_id = ""
        device_id = "85"  # 默认设备ID
        workflow_id = ""  # 下载工作流ID

        try:
            # 根据平台名称查找平台ID
            if task.platform.lower() == "douyin":
                platform_doc = db_service.db.social_platforms.find_one({"id": "douyin"})
                if platform_doc:
                    platform_id = str(platform_doc["_id"])
                    logger.info(f"找到抖音平台ID: {platform_id}")

            # 根据our_account_id查找对应的账号记录
            account_doc = db_service.db.social_accounts.find_one({"_id": task.our_account_id})
            if account_doc:
                account_id = str(account_doc["_id"])
                logger.info(f"找到账号ID: {account_id}")

            # 查找下载工作流ID
            workflow_doc = db_service.db.workflows.find_one({"name": "抖音内容下载"})
            if workflow_doc:
                workflow_id = str(workflow_doc["_id"])
                logger.info(f"找到下载工作流ID: {workflow_id}")

        except Exception as e:
            logger.warning(f"查找平台/账号/工作流信息失败: {str(e)}")

        # 创建下载任务数据（包含Core服务需要的字段）
        task_data = {
            "task_id": str(uuid.uuid4()),
            "task_type": "benchmark_download",
            "status": "pending",

            # 🔧 Core服务需要的字段
            "platform_id": platform_id,
            "account_id": account_id,
            "device_id": device_id,
            "workflow_id": workflow_id,
            "content_path": task.download_path,  # Core服务使用content_path字段

            # 下载任务特有字段
            "our_account_id": task.our_account_id,
            "our_account_name": task.our_account_name,
            "benchmark_account_id": task.benchmark_account_id,
            "benchmark_account_name": task.benchmark_account_name,
            "benchmark_account_url": task.benchmark_account_url,
            "platform": task.platform,
            "download_path": task.download_path,
            "download_config": task.download_config.model_dump(),

            # 通用字段
            "progress": 0,
            "downloaded_count": 0,
            "total_count": 0,
            "created_at": datetime.datetime.now(datetime.timezone.utc),
            "updated_at": datetime.datetime.now(datetime.timezone.utc),
            "estimated_time": 3600,  # 预计1小时
            "error_message": None,
            "logs": []
        }

        # 保存到数据库
        created_task_id = await db_service.create_task(task_data)
        logger.info(f"下载任务创建成功: {created_task_id}")

        # 🔧 修复：立即启动下载任务
        try:
            from app.core.service_discovery import get_core_client

            # 获取Core客户端 - 自动从 Consul 发现服务
            core_client = get_core_client()

            # 创建任务在Core服务中
            logger.info(f"在Core服务中创建下载任务: {created_task_id}")
            create_result = await core_client.create_task(created_task_id, task_data)

            if create_result.get("success"):
                logger.info(f"Core服务任务创建成功，开始启动: {created_task_id}")

                # 启动任务
                start_result = await core_client.start_task(created_task_id)

                if start_result.get("success"):
                    logger.info(f"下载任务启动成功: {created_task_id}")

                    # 更新任务状态为running
                    db_service.update_task(created_task_id, {
                        "status": "running",
                        "start_time": datetime.datetime.now(datetime.timezone.utc)
                    })
                else:
                    logger.error(f"下载任务启动失败: {start_result.get('error')}")
            else:
                logger.error(f"Core服务任务创建失败: {create_result.get('error')}")

        except Exception as e:
            logger.error(f"调用Core服务失败: {str(e)}")
            # 不抛出异常，任务已创建，可以手动启动

        return DownloadTaskResponse(
            task_id=created_task_id,
            status="pending",
            estimated_time=3600,
            download_path=task.download_path,
            created_at=task_data["created_at"]
        )

    except Exception as e:
        logger.error(f"创建下载任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建下载任务失败: {str(e)}")


# 创建批量下载任务
@router.post("/download/batch-tasks", response_model=BatchDownloadTaskResponse)
async def create_batch_download_tasks(
    batch_task: BatchBenchmarkDownloadTaskCreate,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """创建批量对标账号下载任务"""
    logger.info(f"创建批量下载任务: {len(batch_task.tasks)} 个任务")

    try:
        batch_id = str(uuid.uuid4())
        task_ids = []
        total_estimated_time = 0

        # 创建批量任务记录
        batch_data = {
            "batch_id": batch_id,
            "task_type": "benchmark_download_batch",
            "status": "pending",
            "total_tasks": len(batch_task.tasks),
            "completed_tasks": 0,
            "failed_tasks": 0,
            "created_at": datetime.datetime.now(datetime.timezone.utc),
            "updated_at": datetime.datetime.now(datetime.timezone.utc),
            "task_ids": []
        }

        # 为每个任务创建子任务
        for i, task in enumerate(batch_task.tasks):
            # 验证每个对标账号是否存在
            logger.info(f"验证批量任务中的对标账号 {i+1}: {task.benchmark_account_id}")

            # 简化验证，只检查是否能找到对标账号
            benchmark_exists = False
            try:
                if ObjectId.is_valid(task.benchmark_account_id):
                    benchmark_account = db_service.db.benchmark_accounts.find_one({"_id": ObjectId(task.benchmark_account_id)})
                    benchmark_exists = benchmark_account is not None
                else:
                    benchmark_account = db_service.db.benchmark_accounts.find_one({"_id": task.benchmark_account_id})
                    benchmark_exists = benchmark_account is not None
            except Exception as e:
                logger.warning(f"验证对标账号失败: {str(e)}")

            if not benchmark_exists:
                logger.warning(f"批量任务中的对标账号不存在，跳过: {task.benchmark_account_id}")
                continue

            # 创建子任务数据
            subtask_data = {
                "task_id": str(uuid.uuid4()),
                "batch_id": batch_id,
                "task_type": "benchmark_download",
                "status": "pending",
                "our_account_id": task.our_account_id,
                "our_account_name": task.our_account_name,
                "benchmark_account_id": task.benchmark_account_id,
                "benchmark_account_name": task.benchmark_account_name,
                "benchmark_account_url": task.benchmark_account_url,
                "platform": task.platform,
                "download_path": task.download_path,
                "download_config": task.download_config.model_dump(),
                "progress": 0,
                "downloaded_count": 0,
                "total_count": 0,
                "created_at": datetime.datetime.now(datetime.timezone.utc),
                "updated_at": datetime.datetime.now(datetime.timezone.utc),
                "estimated_time": 3600,
                "error_message": None,
                "logs": []
            }

            # 保存子任务
            created_subtask_id = await db_service.create_task(subtask_data)
            task_ids.append(created_subtask_id)
            total_estimated_time += 3600

            logger.info(f"创建子任务 {i+1}/{len(batch_task.tasks)}: {created_subtask_id}")

        # 更新批量任务数据
        batch_data["task_ids"] = task_ids
        batch_data["estimated_time"] = total_estimated_time

        # 保存批量任务
        batch_task_id = await db_service.create_task(batch_data)
        logger.info(f"批量下载任务创建成功: {batch_task_id}")

        # 发送任务到Core服务执行
        # TODO: 实现批量任务分发逻辑
        logger.info(f"批量任务已创建，等待Core服务处理: {batch_id}")

        return BatchDownloadTaskResponse(
            batch_id=batch_id,
            task_ids=task_ids,
            total_tasks=len(task_ids),
            status="pending",
            estimated_time=total_estimated_time,
            created_at=batch_data["created_at"]
        )

    except Exception as e:
        logger.error(f"创建批量下载任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建批量下载任务失败: {str(e)}")

# 获取下载任务列表
@router.get("/download/tasks")
async def get_download_tasks(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    our_account_id: Optional[str] = Query(None),
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """获取下载任务列表"""
    try:
        # 构建查询条件
        query = {"task_type": {"$in": ["benchmark_download", "benchmark_download_batch"]}}

        if status:
            query["status"] = status
        if our_account_id:
            query["our_account_id"] = our_account_id

        # 分页查询 - 修复：查询social_tasks集合
        skip = (page - 1) * limit
        cursor = db_service.db.social_tasks.find(query).sort("created_at", -1).skip(skip).limit(limit)
        tasks = await cursor.to_list(length=limit)

        total = await db_service.db.social_tasks.count_documents(query)

        return {
            "items": tasks,
            "total": total,
            "page": page,
            "limit": limit,
            "has_next": skip + limit < total
        }

    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

# 取消下载任务
@router.delete("/download/tasks/{task_id}")
async def cancel_download_task(
    task_id: str,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """取消下载任务"""
    try:
        task = db_service.get_task_by_id(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 先尝试通知Core服务取消任务
        core_cancel_success = False
        try:
            from app.core.service_discovery import get_core_client
            core_client = get_core_client()

            logger.info(f"通知Core服务取消下载任务: {task_id}")
            cancel_result = await core_client.cancel_task(task_id)
            core_cancel_success = cancel_result.get("success", False)

            if core_cancel_success:
                logger.info(f"Core服务下载任务 {task_id} 取消成功")
            else:
                logger.warning(f"Core服务下载任务 {task_id} 取消失败: {cancel_result.get('error', '未知错误')}")
        except Exception as e:
            logger.error(f"通知Core服务取消下载任务失败: {str(e)}")

        # 更新任务状态
        db_service.update_task(task_id, {
            "status": "cancelled",
            "updated_at": datetime.datetime.now(datetime.timezone.utc),
            "core_canceled": core_cancel_success  # 记录Core服务是否成功取消
        })

        message = "下载任务已取消"
        if not core_cancel_success:
            message += "（数据库状态已更新，但Core服务可能仍在运行）"

        logger.info(f"下载任务取消完成: {task_id}")
        return {"message": message}

    except Exception as e:
        logger.error(f"取消下载任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消下载任务失败: {str(e)}")
