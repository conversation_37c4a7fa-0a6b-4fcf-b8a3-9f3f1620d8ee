"""
文件管理器
负责文件推送、文件选择等操作
"""

import os
import logging
import asyncio
from typing import Dict, Any, Optional
from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logger = logging.getLogger(__name__)


class FileManager:
    """文件管理器类"""

    def __init__(self, device_manager):
        """初始化文件管理器

        Args:
            device_manager: 设备管理器实例
        """
        self.device_manager = device_manager
        # 不再保存静态的device_id，而是动态获取
        self._device_filename = None  # 保存设备上的实际文件名

    async def push_file_to_device(self, local_path: str, remote_path: str = "/sdcard/Pictures/") -> bool:
        """将文件推送到设备

        Args:
            local_path: 本地文件路径
            remote_path: 设备上的目标路径

        Returns:
            bool: 是否推送成功
        """
        try:
            if not os.path.exists(local_path):
                logger.error(f"本地文件不存在: {local_path}")
                return False

            # 获取实际的设备ID
            actual_device_id = self.device_manager.device_id

            # 处理文件名，确保目标路径正确
            filename = os.path.basename(local_path)

            # 如果remote_path是目录，则拼接文件名
            if remote_path.endswith('/'):
                target_path = remote_path + filename
            else:
                target_path = remote_path

            logger.info(f"将文件推送到设备: {local_path} -> {target_path}")
            logger.debug(f"使用设备ID: {actual_device_id}")
            logger.debug(f"原始文件名: {filename}")

            # 使用subprocess.create_subprocess_exec而不是shell，避免特殊字符问题
            push_process = await asyncio.create_subprocess_exec(
                "adb", "-s", actual_device_id, "push", local_path, target_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            push_stdout, push_stderr = await push_process.communicate()

            if push_process.returncode != 0:
                error_msg = push_stderr.decode('utf-8', errors='ignore') if push_stderr else push_stdout.decode('utf-8', errors='ignore')
                logger.error(f"推送文件失败: {error_msg}")

                # 如果是因为文件名问题，尝试重命名后推送
                if "couldn't create file" in error_msg or "Is a directory" in error_msg:
                    logger.info("尝试使用简化文件名重新推送...")
                    return await self._push_with_simple_name(local_path, remote_path, actual_device_id)

                return False

            logger.info("文件已成功推送到设备")
            return True

        except Exception as e:
            logger.error(f"推送文件异常: {str(e)}", exc_info=True)
            return False

    async def _push_with_simple_name(self, local_path: str, remote_path: str, device_id: str) -> bool:
        """使用简化文件名推送文件

        Args:
            local_path: 本地文件路径
            remote_path: 设备上的目标路径
            device_id: 设备ID

        Returns:
            bool: 是否推送成功
        """
        try:
            import time
            import hashlib

            # 获取原始文件名和扩展名
            original_filename = os.path.basename(local_path)
            name, ext = os.path.splitext(original_filename)

            # 生成简化的文件名：使用时间戳和哈希
            timestamp = int(time.time())
            hash_obj = hashlib.md5(original_filename.encode('utf-8'))
            hash_short = hash_obj.hexdigest()[:8]
            simple_filename = f"video_{timestamp}_{hash_short}{ext}"

            # 构建目标路径
            if remote_path.endswith('/'):
                target_path = remote_path + simple_filename
            else:
                target_path = remote_path

            logger.info(f"使用简化文件名推送: {original_filename} -> {simple_filename}")

            # 推送文件
            push_process = await asyncio.create_subprocess_exec(
                "adb", "-s", device_id, "push", local_path, target_path,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            push_stdout, push_stderr = await push_process.communicate()

            if push_process.returncode != 0:
                error_msg = push_stderr.decode('utf-8', errors='ignore') if push_stderr else push_stdout.decode('utf-8', errors='ignore')
                logger.error(f"简化文件名推送也失败: {error_msg}")
                return False

            logger.info(f"文件已成功推送到设备，设备上文件名: {simple_filename}")

            # 保存文件名映射，供后续使用
            self._device_filename = simple_filename
            return True

        except Exception as e:
            logger.error(f"简化文件名推送异常: {str(e)}", exc_info=True)
            return False

    def get_device_filename(self, original_filename: str = None) -> str:
        """获取设备上的实际文件名

        Args:
            original_filename: 原始文件名（可选）

        Returns:
            str: 设备上的实际文件名
        """
        if self._device_filename:
            return self._device_filename
        elif original_filename:
            return os.path.basename(original_filename)
        else:
            return "unknown_file"

    async def select_file_in_picker(self, filename: str, folder_name: str = "Pictures") -> bool:
        """在文件选择器中选择文件

        Args:
            filename: 文件名
            folder_name: 文件夹名称

        Returns:
            bool: 是否选择成功
        """
        try:
            driver = self.device_manager.get_driver()
            if not driver:
                logger.error("没有可用的Appium驱动，无法操作文件选择器")
                return False

            logger.info(f"在文件选择器中选择文件: {filename}")

            # 尝试点击指定文件夹
            try:
                folder_xpath_options = [
                    f"//android.widget.TextView[@text='{folder_name}']",
                    f"//android.widget.TextView[@text='Pictures']",
                    f"//android.widget.TextView[@text='图片']"
                ]

                folder_found = False
                for xpath in folder_xpath_options:
                    try:
                        folder_element = WebDriverWait(driver, 3).until(
                            EC.element_to_be_clickable((AppiumBy.XPATH, xpath))
                        )
                        folder_element.click()
                        logger.info(f"已点击文件夹: {xpath}")
                        folder_found = True
                        await asyncio.sleep(2)
                        break
                    except:
                        continue

                if not folder_found:
                    logger.warning("未找到指定文件夹，尝试直接选择文件")

            except Exception as folder_error:
                logger.warning(f"点击文件夹异常: {str(folder_error)}")

            # 尝试选择文件
            try:
                file_element = WebDriverWait(driver, 10).until(
                    EC.element_to_be_clickable((AppiumBy.XPATH, f"//android.widget.TextView[@text='{filename}']"))
                )
                file_element.click()
                logger.info(f"已选择文件: {filename}")
                await asyncio.sleep(2)
                return True

            except Exception as file_error:
                logger.warning(f"选择文件失败: {str(file_error)}")

                # 尝试使用坐标点击
                logger.info("尝试使用坐标点击选择文件")
                screen_size = driver.get_window_size()
                width = screen_size['width']
                height = screen_size['height']

                # 点击屏幕中间位置的文件
                driver.tap([(width // 2, height // 3)])
                await asyncio.sleep(2)
                logger.info("已使用坐标点击选择文件")
                return True

        except Exception as e:
            logger.error(f"在文件选择器中选择文件异常: {str(e)}", exc_info=True)
            return False

    async def get_file_list(self, remote_path: str = "/sdcard/Pictures/") -> list:
        """获取设备上指定目录的文件列表

        Args:
            remote_path: 设备上的目录路径

        Returns:
            list: 文件列表
        """
        try:
            # 获取实际的设备ID
            actual_device_id = self.device_manager.device_id
            logger.info(f"获取设备目录文件列表: {remote_path}")
            logger.debug(f"使用设备ID: {actual_device_id}")

            list_process = await asyncio.create_subprocess_shell(
                f"adb -s {actual_device_id} shell ls \"{remote_path}\"",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            list_stdout, list_stderr = await list_process.communicate()

            if list_process.returncode != 0:
                error_msg = list_stderr.decode('utf-8', errors='ignore') if list_stderr else list_stdout.decode('utf-8', errors='ignore')
                logger.error(f"获取文件列表失败: {error_msg}")
                return []

            files_output = list_stdout.decode('utf-8', errors='ignore').strip()
            files = files_output.split('\n')
            files = [f.strip() for f in files if f.strip()]
            logger.info(f"找到 {len(files)} 个文件")
            return files

        except Exception as e:
            logger.error(f"获取文件列表异常: {str(e)}", exc_info=True)
            return []

    async def delete_file_from_device(self, remote_path: str) -> bool:
        """从设备删除文件

        Args:
            remote_path: 设备上的文件路径

        Returns:
            bool: 是否删除成功
        """
        try:
            # 获取实际的设备ID
            actual_device_id = self.device_manager.device_id
            logger.info(f"从设备删除文件: {remote_path}")
            logger.debug(f"使用设备ID: {actual_device_id}")

            delete_process = await asyncio.create_subprocess_shell(
                f"adb -s {actual_device_id} shell rm \"{remote_path}\"",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            delete_stdout, delete_stderr = await delete_process.communicate()

            if delete_process.returncode != 0:
                error_msg = delete_stderr.decode('utf-8', errors='ignore') if delete_stderr else delete_stdout.decode('utf-8', errors='ignore')
                logger.error(f"删除文件失败: {error_msg}")
                return False

            logger.info("文件已成功删除")
            return True

        except Exception as e:
            logger.error(f"删除文件异常: {str(e)}", exc_info=True)
            return False

    async def cleanup_uploaded_file(self, original_filename: str, remote_dir: str = "/sdcard/Pictures/") -> bool:
        """清理上传的视频文件

        Args:
            original_filename: 原始文件名
            remote_dir: 设备上的目录路径

        Returns:
            bool: 是否清理成功
        """
        try:
            logger.info(f"🧹 开始清理上传的视频文件: {original_filename}")

            # 获取设备上的实际文件名
            device_filename = self.get_device_filename(original_filename)

            # 构建完整的文件路径
            if remote_dir.endswith('/'):
                remote_file_path = remote_dir + device_filename
            else:
                remote_file_path = remote_dir + '/' + device_filename

            logger.info(f"🗑️ 删除设备文件: {remote_file_path}")

            # 删除文件
            success = await self.delete_file_from_device(remote_file_path)

            if success:
                logger.info(f"✅ 成功清理视频文件: {device_filename}")
                # 清除文件名映射
                self._device_filename = None
                return True
            else:
                logger.warning(f"⚠️ 清理视频文件失败: {device_filename}")
                return False

        except Exception as e:
            logger.error(f"清理上传文件异常: {str(e)}", exc_info=True)
            return False
