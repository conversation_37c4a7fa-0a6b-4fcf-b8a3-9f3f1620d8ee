"""
任务管理API
独立的任务调度和管理接口
"""

import logging
import datetime
import json
import asyncio
from fastapi import APIRouter, Request, Query
from typing import Optional
from bson import ObjectId
from app.core.schemas.social_repository import SocialDatabaseService

logger = logging.getLogger(__name__)

# 全局后台任务管理器
class BackgroundTaskManager:
    def __init__(self):
        self.tasks = set()

    def add_task(self, task):
        """添加任务并设置完成回调"""
        self.tasks.add(task)
        task.add_done_callback(self._task_done_callback)
        return task

    def _task_done_callback(self, task):
        """任务完成回调，清理引用"""
        self.tasks.discard(task)
        if task.exception():
            logger.error(f"后台任务异常: {task.exception()}")

    def cancel_all(self):
        """取消所有任务"""
        for task in list(self.tasks):
            if not task.done():
                task.cancel()
        self.tasks.clear()

# 全局实例
background_task_manager = BackgroundTaskManager()

async def start_core_task_async(task_id: str, task_data: dict, db_service, parent_task_id: str = None) -> None:
    """异步启动Core服务任务，避免阻塞前端请求

    Args:
        task_id: 任务ID
        task_data: 任务数据
        db_service: 数据库服务
        parent_task_id: 父任务ID（如果是子任务）
    """
    try:
        import asyncio
        from app.core.service_discovery import get_core_client

        logger.info(f"🚀 后台异步启动Core服务任务: {task_id}")

        # 获取Core客户端 - 自动从 Consul 发现服务
        core_client = get_core_client()

        # 构建完整的任务数据
        subtask_metadata = task_data.get("metadata", {})

        # 如果子任务没有metadata，从父任务获取
        if not subtask_metadata and task_data.get("parent_task_id"):
            parent_task = db_service.db.social_tasks.find_one({"task_id": task_data.get("parent_task_id")})
            if parent_task:
                subtask_metadata = parent_task.get("metadata", {})
                logger.info(f"从父任务继承metadata: {subtask_metadata}")

        # 🔧 重要修复：获取平台的实际ID而不是ObjectId
        platform_object_id = task_data.get("platform_id", "")
        platform_actual_id = platform_object_id  # 默认值

        try:
            # 查询平台信息获取实际的平台ID
            platform_doc = db_service.db.social_platforms.find_one({"_id": platform_object_id})
            if platform_doc and "id" in platform_doc:
                platform_actual_id = platform_doc["id"]
                logger.info(f"✅ 平台ID映射: {platform_object_id} -> {platform_actual_id}")
            else:
                logger.warning(f"⚠️ 未找到平台信息: {platform_object_id}")
        except Exception as e:
            logger.warning(f"⚠️ 查询平台信息失败: {str(e)}")

        core_task_data = {
            "task_id": task_id,
            "platform_id": platform_actual_id,  # 使用实际的平台ID
            "platform_object_id": str(platform_object_id),  # 保留ObjectId用于其他用途
            "account_id": str(task_data.get("account_id", "")),
            "device_id": str(task_data.get("device_id", "85")),
            "content_path": task_data.get("content_path", ""),
            "workflow_id": str(task_data.get("workflow_id", "")),
            "params": task_data.get("params", {}),
            "metadata": subtask_metadata
        }

        logger.info(f"传递给Core的任务数据: {core_task_data}")

        # 创建任务
        try:
            create_result = await asyncio.wait_for(
                core_client.create_task(task_id, core_task_data),
                timeout=120.0  # 2分钟超时
            )
        except asyncio.TimeoutError:
            logger.error(f"创建Core服务任务{task_id}超时")
            raise Exception("创建任务超时，请检查Core服务状态")

        if create_result.get("success"):
            logger.info(f"Core服务任务{task_id}创建成功")

            # 启动任务
            logger.info(f"启动Core服务任务: {task_id}")
            try:
                start_result = await asyncio.wait_for(
                    core_client.start_task(task_id),
                    timeout=60.0  # 1分钟超时
                )
            except asyncio.TimeoutError:
                logger.error(f"启动Core服务任务{task_id}超时")
                raise Exception("启动任务超时，请检查Core服务状态")

            if start_result.get("success"):
                logger.info(f"✅ Core服务任务{task_id}启动成功")

                # 如果有父任务，同步更新主任务状态
                if parent_task_id:
                    await sync_main_task_status(db_service, parent_task_id)

            else:
                logger.error(f"❌ Core服务任务{task_id}启动失败: {start_result.get('error')}")
                # 启动失败，将任务状态改回failed
                db_service.db.social_tasks.update_one(
                    {"task_id": task_id},
                    {"$set": {
                        "status": "failed",
                        "end_time": datetime.datetime.now().isoformat(),
                        "updated_at": datetime.datetime.now()
                    }}
                )

                # 如果有父任务，同步更新主任务状态
                if parent_task_id:
                    await sync_main_task_status(db_service, parent_task_id)
        else:
            logger.error(f"❌ Core服务任务{task_id}创建失败: {create_result.get('error')}")
            # 创建失败，将任务状态改回failed
            db_service.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": {
                    "status": "failed",
                    "end_time": datetime.datetime.now().isoformat(),
                    "updated_at": datetime.datetime.now()
                }}
            )

    except Exception as e:
        logger.error(f"❌ 异步启动Core服务任务{task_id}失败: {str(e)}", exc_info=True)
        # Core服务调用失败，将任务状态改回failed
        try:
            db_service.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": {
                    "status": "failed",
                    "end_time": datetime.datetime.now().isoformat(),
                    "updated_at": datetime.datetime.now()
                }}
            )
        except Exception as db_error:
            logger.error(f"更新任务状态失败: {str(db_error)}")


async def sync_main_task_status(db_service, main_task_id: str) -> None:
    """同步主任务状态

    根据子任务的状态自动更新主任务状态：
    - 如果有子任务在运行，主任务为running
    - 如果所有子任务都完成，主任务为completed
    - 如果有子任务失败且没有运行中的，主任务为failed
    - 如果所有子任务都等待中，主任务为pending
    """
    try:
        logger.info(f"🔄 同步主任务状态: {main_task_id}")

        # 获取所有子任务
        subtasks = list(db_service.db.social_tasks.find({"parent_task_id": main_task_id}))

        if not subtasks:
            logger.warning(f"主任务 {main_task_id} 没有子任务")
            return

        # 统计子任务状态
        status_count = {}
        for subtask in subtasks:
            status = subtask.get("status", "unknown")
            status_count[status] = status_count.get(status, 0) + 1

        logger.info(f"子任务状态统计: {status_count}")

        # 确定主任务状态
        new_main_status = None
        completed_count = status_count.get("completed", 0)
        failed_count = status_count.get("failed", 0)
        running_count = status_count.get("running", 0)
        pending_count = status_count.get("pending", 0)
        canceled_count = status_count.get("canceled", 0)
        paused_count = status_count.get("paused", 0)

        total_subtasks = len(subtasks)

        # 状态优先级判断
        if running_count > 0:
            # 有子任务在运行
            new_main_status = "running"
        elif pending_count > 0 and failed_count == 0 and canceled_count == 0:
            # 有等待中的任务，且没有失败或取消的
            new_main_status = "pending"
        elif paused_count > 0 and running_count == 0:
            # 有暂停的任务，且没有运行中的
            new_main_status = "paused"
        elif completed_count == total_subtasks:
            # 所有子任务都完成
            new_main_status = "completed"
        elif failed_count > 0 and running_count == 0 and pending_count == 0:
            # 有失败的任务，且没有运行中或等待中的
            new_main_status = "failed"
        elif canceled_count == total_subtasks:
            # 所有子任务都被取消
            new_main_status = "canceled"
        else:
            # 混合状态，保持运行中
            new_main_status = "running"

        # 获取当前主任务状态
        main_task = db_service.db.social_tasks.find_one({"task_id": main_task_id})
        if not main_task:
            logger.error(f"主任务 {main_task_id} 不存在")
            return

        current_status = main_task.get("status")

        # 如果状态需要更新
        if current_status != new_main_status:
            logger.info(f"更新主任务 {main_task_id} 状态: {current_status} -> {new_main_status}")

            update_data = {
                "status": new_main_status,
                "updated_at": datetime.datetime.now(),
                "completed_subtasks": completed_count,
                "progress": int((completed_count / total_subtasks) * 100) if total_subtasks > 0 else 0
            }

            # 如果主任务变为完成或失败，设置结束时间
            if new_main_status in ["completed", "failed", "canceled"]:
                update_data["end_time"] = datetime.datetime.now().isoformat()
            elif new_main_status == "running" and not main_task.get("start_time"):
                # 如果主任务开始运行但没有开始时间，设置开始时间
                update_data["start_time"] = datetime.datetime.now().isoformat()

            # 更新数据库
            db_service.db.social_tasks.update_one(
                {"task_id": main_task_id},
                {"$set": update_data}
            )

            logger.info(f"✅ 主任务 {main_task_id} 状态同步完成: {new_main_status}")
        else:
            # 即使状态不变，也要检查时间戳是否完整
            logger.info(f"主任务 {main_task_id} 状态无需更新: {current_status}")

            # 🔧 检查并修复缺失的时间戳
            timestamp_update = {}

            # 检查结束时间
            if current_status in ["completed", "failed", "canceled"] and not main_task.get("end_time"):
                timestamp_update["end_time"] = datetime.datetime.now().isoformat()
                logger.info(f"🔧 为主任务 {main_task_id} 添加缺失的结束时间")

            # 检查开始时间
            if current_status in ["running", "completed", "failed", "canceled"] and not main_task.get("start_time"):
                timestamp_update["start_time"] = datetime.datetime.now().isoformat()
                logger.info(f"🔧 为主任务 {main_task_id} 添加缺失的开始时间")

            # 如果需要更新时间戳
            if timestamp_update:
                timestamp_update["updated_at"] = datetime.datetime.now()
                db_service.db.social_tasks.update_one(
                    {"task_id": main_task_id},
                    {"$set": timestamp_update}
                )
                logger.info(f"✅ 主任务 {main_task_id} 时间戳修复完成: {list(timestamp_update.keys())}")

        # 🔧 同时检查并修复所有子任务的时间戳
        for subtask in subtasks:
            subtask_id = subtask.get("task_id")
            subtask_status = subtask.get("status")

            if not subtask_id:
                continue

            subtask_timestamp_update = {}

            # 检查子任务的结束时间
            if subtask_status in ["completed", "failed", "canceled"] and not subtask.get("end_time"):
                subtask_timestamp_update["end_time"] = datetime.datetime.now().isoformat()
                logger.info(f"🔧 为子任务 {subtask_id} 添加缺失的结束时间")

            # 检查子任务的开始时间
            if subtask_status in ["running", "completed", "failed", "canceled"] and not subtask.get("start_time"):
                subtask_timestamp_update["start_time"] = datetime.datetime.now().isoformat()
                logger.info(f"🔧 为子任务 {subtask_id} 添加缺失的开始时间")

            # 如果需要更新子任务时间戳
            if subtask_timestamp_update:
                subtask_timestamp_update["updated_at"] = datetime.datetime.now()
                db_service.db.social_tasks.update_one(
                    {"task_id": subtask_id},
                    {"$set": subtask_timestamp_update}
                )
                logger.info(f"✅ 子任务 {subtask_id} 时间戳修复完成: {list(subtask_timestamp_update.keys())}")

    except Exception as e:
        logger.error(f"同步主任务状态失败: {str(e)}", exc_info=True)


async def get_task_failure_info(db_service, task_id: str) -> dict:
    """获取任务失败的详细信息"""
    try:
        # 获取任务的错误日志
        cursor = db_service.db.social_task_logs.find({
            "task_id": task_id,
            "level": "error"
        }).sort("created_at", -1).limit(5)
        error_logs = await cursor.to_list(length=5)  # 获取最近5条错误日志

        failure_info = {
            "failure_reason": "任务执行失败",
            "failure_step": "未知步骤",
            "failure_details": "无详细信息",
            "failure_suggestion": "请查看详细日志了解具体原因"
        }

        if error_logs:
            # 获取最后一个错误日志
            last_error = error_logs[0]
            error_message = last_error.get("message", "")

            # 解析错误信息，提取失败步骤
            if "步骤失败:" in error_message:
                # 提取步骤失败信息
                parts = error_message.split("步骤失败:")
                if len(parts) > 1:
                    step_name = parts[1].strip()
                    failure_info["failure_step"] = step_name
                    failure_info["failure_reason"] = f"步骤失败: {step_name}"

                    # 根据失败步骤提供具体建议
                    if "选择上传视频选项" in step_name:
                        failure_info["failure_suggestion"] = "YouTube应用界面可能发生变化，请检查元素定位配置或应用版本"
                    elif "连接设备" in step_name:
                        failure_info["failure_suggestion"] = "请检查设备是否正常运行，Appium服务是否启动"
                    elif "启动应用" in step_name:
                        failure_info["failure_suggestion"] = "请检查应用是否已安装，版本是否兼容"
                    elif "网络" in step_name or "连接" in step_name:
                        failure_info["failure_suggestion"] = "请检查网络连接，确保设备能正常访问YouTube"
                    elif "元素" in step_name or "定位" in step_name:
                        failure_info["failure_suggestion"] = "界面元素定位失败，可能是应用界面更新，请检查元素配置"
                    elif "上传" in step_name:
                        failure_info["failure_suggestion"] = "上传过程失败，请检查网络连接和文件格式"
                    else:
                        failure_info["failure_suggestion"] = f"'{step_name}'步骤执行失败，请查看详细日志了解具体原因"

            elif "上传失败" in error_message:
                # 处理上传失败的情况
                failure_info["failure_reason"] = "视频上传失败"
                if ":" in error_message:
                    parts = error_message.split(":")
                    if len(parts) > 1:
                        detail = parts[-1].strip()
                        failure_info["failure_step"] = detail
                        failure_info["failure_suggestion"] = "上传过程中遇到问题，请检查网络连接和视频文件"

            elif "连接" in error_message:
                failure_info["failure_reason"] = "设备连接失败"
                failure_info["failure_step"] = "设备连接"
                failure_info["failure_suggestion"] = "请检查设备是否正常运行，Appium服务是否启动"

            else:
                # 通用错误处理
                failure_info["failure_reason"] = error_message[:50] + "..." if len(error_message) > 50 else error_message
                failure_info["failure_step"] = "执行过程"
                failure_info["failure_suggestion"] = "请查看详细日志了解具体原因"

            # 构建详细错误信息
            all_errors = [log.get("message", "") for log in error_logs]
            failure_info["failure_details"] = "\n".join(all_errors[:3])  # 最多显示3条错误

        return failure_info

    except Exception as e:
        logger.error(f"获取任务失败信息异常: {str(e)}")
        return {
            "failure_reason": "获取失败信息异常",
            "failure_step": "未知",
            "failure_details": str(e),
            "failure_suggestion": "请联系技术支持"
        }

def init_task_routes():
    """初始化任务管理路由"""
    router = APIRouter(
        prefix="/api/tasks",
        tags=["tasks"],
        responses={404: {"description": "Not found"}},
    )

    @router.get("/")
    async def get_tasks(
        request: Request,
        status: Optional[str] = Query(None, description="任务状态过滤"),
        platform_id: Optional[str] = Query(None, description="平台ID过滤"),
        limit: int = Query(20, description="返回数量限制"),
        offset: int = Query(0, description="偏移量")
    ):
        """获取任务列表"""
        try:
            logger.info(f"获取任务列表: status={status}, platform_id={platform_id}, limit={limit}, offset={offset}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 构建查询条件
            query = {}
            if status:
                query["status"] = status
            if platform_id:
                query["platform_id"] = platform_id

            logger.info(f"查询条件: {query}")

            # 查询任务
            tasks_cursor = db_service.db.social_tasks.find(query).sort("created_at", -1).skip(offset).limit(limit)
            tasks = await tasks_cursor.to_list(length=limit)

            # 格式化任务数据（使用与running API相同的逻辑）
            formatted_tasks = []
            for task in tasks:
                if '_id' in task:
                    del task['_id']

                # 🔧 重要：先获取task_type
                task_type = task.get("task_type", "")

                # 获取平台信息
                platform_name = "未知平台"
                platform_id_val = task.get("platform_id", "")

                # 🔧 安全修复：为采集任务和下载任务特殊处理，不影响现有任务
                if task_type == "collect":
                    # 采集任务使用platform字段
                    platform_name = task.get("platform", "未知平台")
                    if platform_name.lower() == "douyin":
                        platform_name = "抖音"
                    elif platform_name.lower() == "youtube":
                        platform_name = "YouTube"
                    elif platform_name.lower() == "tiktok":
                        platform_name = "TikTok"
                elif task_type == "benchmark_download" or task_type == "benchmark_download_batch":
                    # 下载任务使用platform字段
                    platform_name = task.get("platform", "未知平台")
                    if platform_name.lower() == "douyin":
                        platform_name = "抖音"
                    elif platform_name.lower() == "youtube":
                        platform_name = "YouTube"
                    elif platform_name.lower() == "tiktok":
                        platform_name = "TikTok"

                # 原有逻辑保持不变
                if platform_id_val:
                    try:
                        if isinstance(platform_id_val, str) and len(platform_id_val) == 24:
                            platform = await db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id_val)})
                        else:
                            platform = await db_service.db.social_platforms.find_one({"_id": platform_id_val})

                        if platform:
                            platform_name = platform.get("name", platform.get("id", "未知平台"))
                    except Exception as e:
                        logger.warning(f"获取平台信息失败: {e}")

                # 获取账号信息
                account_name = "未知账号"
                account_id = task.get("account_id", "")

                # 🔧 安全修复：为采集任务和下载任务特殊处理，不影响现有任务
                if task_type == "collect":
                    # 采集任务使用account_name字段
                    account_name = task.get("account_name", "未知账号")
                elif task_type == "benchmark_download" or task_type == "benchmark_download_batch":
                    # 下载任务使用our_account_name字段
                    account_name = task.get("our_account_name", "未知账号")
                    account_id = task.get("our_account_id", "")

                # 原有逻辑保持不变
                if account_id and (task_type not in ["collect", "benchmark_download", "benchmark_download_batch"]):
                    try:
                        if isinstance(account_id, str) and len(account_id) == 24:
                            account = await db_service.db.social_accounts.find_one({"_id": ObjectId(account_id)})
                        else:
                            account = await db_service.db.social_accounts.find_one({"_id": account_id})

                        if account:
                            account_name = account.get("display_name", account.get("username", "未知账号"))
                    except Exception as e:
                        logger.warning(f"获取账号信息失败: {e}")

                # 动态生成工作流名称
                workflow_name = task.get("workflow_name", "")
                content_type = task.get("content_type", "")
                task_type = task.get("task_type", "")

                if not workflow_name:
                    # 🔧 新增：为采集任务生成工作流名称
                    if task_type == "collect":
                        platform = task.get("platform", "")
                        if platform.lower() == "douyin":
                            workflow_name = "抖音内容采集"
                        elif platform.lower() == "youtube":
                            workflow_name = "YouTube内容采集"
                        elif platform.lower() == "tiktok":
                            workflow_name = "TikTok内容采集"
                        else:
                            workflow_name = f"{platform}内容采集" if platform else "内容采集"
                    # 🔧 新增：为下载任务生成工作流名称
                    elif task_type == "benchmark_download":
                        platform = task.get("platform", "")
                        if platform.lower() == "douyin":
                            workflow_name = "抖音内容下载"
                        elif platform.lower() == "youtube":
                            workflow_name = "YouTube内容下载"
                        elif platform.lower() == "tiktok":
                            workflow_name = "TikTok内容下载"
                        else:
                            workflow_name = f"{platform}内容下载" if platform else "对标账号内容下载"
                    elif task_type == "benchmark_download_batch":
                        workflow_name = "批量内容下载"
                    else:
                        # 原有的YouTube上传任务逻辑
                        metadata = task.get("metadata", {})
                        if isinstance(metadata, dict):
                            content_type = metadata.get("contentType", content_type)

                        if content_type == "shorts":
                            workflow_name = "YouTube短视频上传"
                        elif content_type == "video":
                            workflow_name = "YouTube视频上传"
                        elif content_type == "live":
                            workflow_name = "YouTube直播"
                        elif content_type == "post":
                            workflow_name = "YouTube社区发布"
                        elif content_type:
                            workflow_name = f"YouTube{content_type}上传"

                formatted_task = {
                    "id": task.get("task_id", task.get("id", "")),
                    "platform_id": platform_id_val,
                    "platform_name": platform_name,
                    "account_id": account_id,
                    "account_name": account_name,
                    "device_id": task.get("device_id", ""),
                    "content_path": task.get("content_path", ""),
                    "status": task.get("status", "unknown"),
                    "progress": task.get("progress", 0),
                    "task_type": task.get("task_type", "single"),
                    "created_at": task.get("created_at", ""),
                    "updated_at": task.get("updated_at", ""),
                    "start_time": task.get("start_time", ""),
                    "workflow_id": task.get("workflow_id", ""),
                    "workflow_name": workflow_name,
                    "content_type": content_type,
                    "video_file": task.get("video_file", "")
                }

                # 如果是主任务，添加子任务信息
                if task.get("task_type") == "main":
                    formatted_task["total_subtasks"] = task.get("total_subtasks", 0)
                    formatted_task["completed_subtasks"] = task.get("completed_subtasks", 0)

                # 如果是子任务，添加父任务信息
                if task.get("task_type") == "subtask":
                    formatted_task["parent_task_id"] = task.get("parent_task_id", "")
                    formatted_task["subtask_index"] = task.get("subtask_index", 1)

                formatted_tasks.append(formatted_task)

            # 获取总数
            total_count = await db_service.db.social_tasks.count_documents(query)

            logger.info(f"找到 {len(formatted_tasks)} 个任务，总计 {total_count} 个")

            return {
                "tasks": formatted_tasks,
                "total": total_count,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "has_more": total_count > offset + len(formatted_tasks)
                }
            }

        except Exception as e:
            logger.error(f"获取任务列表失败: {str(e)}", exc_info=True)
            return {
                "tasks": [],
                "total": 0,
                "error": f"获取任务列表失败: {str(e)}"
            }

    @router.get("/stats")
    async def get_task_stats(request: Request):
        """获取任务统计"""
        try:
            logger.info("获取任务统计")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 统计各状态的任务数量
            status_stats = {
                "running": await db_service.db.social_tasks.count_documents({"status": "running"}),
                "pending": await db_service.db.social_tasks.count_documents({"status": "pending"}),
                "paused": await db_service.db.social_tasks.count_documents({"status": "paused"}),
                "completed": await db_service.db.social_tasks.count_documents({"status": "completed"}),
                "failed": await db_service.db.social_tasks.count_documents({"status": "failed"}),
                "canceled": await db_service.db.social_tasks.count_documents({"status": "canceled"})
            }

            # 计算总任务数
            total_tasks = sum(status_stats.values())

            # 今日统计
            today_start = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_stats = {
                "created_today": await db_service.db.social_tasks.count_documents({
                    "created_at": {"$gte": today_start.isoformat()}
                }),
                "completed_today": await db_service.db.social_tasks.count_documents({
                    "status": "completed",
                    "end_time": {"$gte": today_start.isoformat()}
                }),
                "failed_today": await db_service.db.social_tasks.count_documents({
                    "status": "failed",
                    "end_time": {"$gte": today_start.isoformat()}
                })
            }

            logger.info(f"任务统计: {status_stats}, 总计: {total_tasks}")

            return {
                "status_stats": status_stats,
                "today_stats": today_stats,
                "total_tasks": total_tasks
            }

        except Exception as e:
            logger.error(f"获取任务统计失败: {str(e)}", exc_info=True)
            return {
                "status_stats": {
                    "running": 0,
                    "pending": 0,
                    "paused": 0,
                    "completed": 0,
                    "failed": 0,
                    "canceled": 0
                },
                "today_stats": {
                    "created_today": 0,
                    "completed_today": 0,
                    "failed_today": 0
                },
                "total_tasks": 0,
                "error": f"获取统计失败: {str(e)}"
            }

    @router.get("/detail/{task_id}")
    async def get_task_detail(
        task_id: str,
        request: Request
    ):
        """获取任务详情"""
        try:
            logger.info(f"获取任务详情: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 查询任务
            task = await db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {
                    "task": None,
                    "error": "任务不存在"
                }

            # 移除MongoDB的_id字段
            if '_id' in task:
                del task['_id']

            # 获取平台信息
            platform_name = "未知平台"
            platform_id = task.get("platform_id", "")
            if platform_id:
                try:
                    if isinstance(platform_id, str) and len(platform_id) == 24:
                        platform = await db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                    else:
                        platform = await db_service.db.social_platforms.find_one({"_id": platform_id})

                    if platform:
                        platform_name = platform.get("name", platform.get("id", "未知平台"))
                except Exception as e:
                    logger.warning(f"获取平台信息失败: {e}")

            # 获取账号信息
            account_name = "未知账号"
            account_id = task.get("account_id", "")
            if account_id:
                try:
                    if isinstance(account_id, str) and len(account_id) == 24:
                        account = await db_service.db.social_accounts.find_one({"_id": ObjectId(account_id)})
                    else:
                        account = await db_service.db.social_accounts.find_one({"_id": account_id})

                    if account:
                        account_name = account.get("display_name", account.get("username", "未知账号"))
                except Exception as e:
                    logger.warning(f"获取账号信息失败: {e}")

            # 格式化任务数据
            formatted_task = {
                "id": task.get("task_id", task.get("id", "")),
                "platform_id": platform_id,
                "platform_name": platform_name,
                "account_id": account_id,
                "account_name": account_name,
                "device_id": task.get("device_id", ""),
                "content_path": task.get("content_path", ""),
                "status": task.get("status", "unknown"),
                "progress": task.get("progress", 100 if task.get("status") == "completed" else 0),
                "created_at": task.get("created_at", ""),
                "updated_at": task.get("updated_at", ""),
                "start_time": task.get("start_time", ""),
                "end_time": task.get("end_time", ""),
                "estimated_end_time": task.get("estimated_end_time", ""),
                "workflow_id": task.get("workflow_id", ""),
                "params": task.get("params", {})
            }

            # 如果任务失败，获取具体的失败信息
            if task.get("status") == "failed":
                failure_info = await get_task_failure_info(db_service, task.get("task_id"))
                formatted_task.update(failure_info)

            logger.info(f"找到任务: {formatted_task['id']}, 状态: {formatted_task['status']}")

            return {
                "task": formatted_task
            }

        except Exception as e:
            logger.error(f"获取任务详情失败: {str(e)}", exc_info=True)
            return {
                "task": None,
                "error": f"获取任务详情失败: {str(e)}"
            }

    @router.get("/detail/{task_id}/logs")
    async def get_task_logs_detail(
        task_id: str,
        request: Request,
        limit: int = Query(100, description="日志数量限制"),
        offset: int = Query(0, description="偏移量")
    ):
        """获取任务日志"""
        try:
            logger.info(f"获取任务日志: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = await db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {
                    "logs": [],
                    "total": 0,
                    "error": "任务不存在"
                }

            # 从数据库获取任务日志
            logs_cursor = db_service.db.social_task_logs.find(
                {"task_id": task_id}
            ).sort("created_at", 1).skip(offset).limit(limit)
            logs = await logs_cursor.to_list(length=limit)

            # 格式化日志
            formatted_logs = []
            for log in logs:
                if '_id' in log:
                    del log['_id']
                formatted_logs.append({
                    "message": log.get("message", ""),
                    "level": log.get("level", "info"),
                    "timestamp": log.get("timestamp", log.get("created_at", "")),
                    "created_at": log.get("created_at", "")
                })

            # 获取总日志数
            total_logs = await db_service.db.social_task_logs.count_documents({"task_id": task_id})

            logger.info(f"找到 {len(formatted_logs)} 条日志，总计 {total_logs} 条")

            return {
                "logs": formatted_logs,
                "total": total_logs,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "has_more": total_logs > offset + len(formatted_logs)
                }
            }

        except Exception as e:
            logger.error(f"获取任务日志失败: {str(e)}", exc_info=True)
            return {
                "logs": [],
                "total": 0,
                "error": f"获取任务日志失败: {str(e)}"
            }

    @router.get("/running")
    async def get_running_tasks(request: Request):
        """获取所有任务（用于任务管理页面显示）"""
        try:
            logger.info("获取所有任务")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 🔧 修复：查询所有任务，包括已完成的任务
            # 任务管理页面需要显示所有状态的任务，以便用户查看完整的任务列表
            query = {
                "status": {"$in": ["running", "pending", "paused", "failed", "canceled", "completed"]}
            }

            # 先统计总数
            total_count = await db_service.db.social_tasks.count_documents(query)
            logger.info(f"数据库中符合条件的任务总数: {total_count}")

            # 查询任务 - 增加限制到500个，并添加调试信息
            cursor = db_service.db.social_tasks.find(query).sort("_id", -1).limit(500)
            tasks = await cursor.to_list(length=500)
            logger.info(f"实际查询到的任务数量: {len(tasks)}")

            # 格式化任务数据
            formatted_tasks = []
            for task in tasks:
                if '_id' in task:
                    del task['_id']

                # 🔧 重要：先获取task_type
                task_type = task.get("task_type", "")

                # 获取平台信息
                platform_name = "未知平台"
                platform_id = task.get("platform_id", "")

                # 🔧 安全修复：为采集任务和下载任务特殊处理，不影响现有任务
                if task_type == "collect":
                    # 采集任务使用platform字段
                    platform_name = task.get("platform", "未知平台")
                    if platform_name.lower() == "douyin":
                        platform_name = "抖音"
                    elif platform_name.lower() == "youtube":
                        platform_name = "YouTube"
                    elif platform_name.lower() == "tiktok":
                        platform_name = "TikTok"
                elif task_type == "benchmark_download" or task_type == "benchmark_download_batch":
                    # 下载任务使用platform字段
                    platform_name = task.get("platform", "未知平台")
                    if platform_name.lower() == "douyin":
                        platform_name = "抖音"
                    elif platform_name.lower() == "youtube":
                        platform_name = "YouTube"
                    elif platform_name.lower() == "tiktok":
                        platform_name = "TikTok"

                # 原有逻辑保持不变
                if platform_id:
                    try:
                        if isinstance(platform_id, str) and len(platform_id) == 24:
                            platform = await db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                        else:
                            platform = await db_service.db.social_platforms.find_one({"_id": platform_id})

                        if platform:
                            platform_name = platform.get("name", platform.get("id", "未知平台"))
                    except Exception as e:
                        logger.warning(f"获取平台信息失败: {e}")

                # 获取账号信息
                account_name = "未知账号"
                account_id = task.get("account_id", "")

                # 🔧 安全修复：为采集任务和下载任务特殊处理，不影响现有任务
                if task_type == "collect":
                    # 采集任务使用account_name字段
                    account_name = task.get("account_name", "未知账号")
                elif task_type == "benchmark_download" or task_type == "benchmark_download_batch":
                    # 下载任务使用our_account_name字段
                    account_name = task.get("our_account_name", "未知账号")
                    account_id = task.get("our_account_id", "")

                # 原有逻辑保持不变
                if account_id and (task_type not in ["collect", "benchmark_download", "benchmark_download_batch"]):
                    try:
                        if isinstance(account_id, str) and len(account_id) == 24:
                            account = await db_service.db.social_accounts.find_one({"_id": ObjectId(account_id)})
                        else:
                            account = await db_service.db.social_accounts.find_one({"_id": account_id})

                        if account:
                            account_name = account.get("display_name", account.get("username", "未知账号"))
                    except Exception as e:
                        logger.warning(f"获取账号信息失败: {e}")

                # 动态生成工作流名称（兼容旧数据）
                workflow_name = task.get("workflow_name", "")
                content_type = task.get("content_type", "")
                task_type = task.get("task_type", "")

                # 如果没有workflow_name，根据任务类型生成
                if not workflow_name:
                    # 🔧 新增：为采集任务生成工作流名称
                    if task_type == "collect":
                        platform = task.get("platform", "")
                        if platform.lower() == "douyin":
                            workflow_name = "抖音内容采集"
                        elif platform.lower() == "youtube":
                            workflow_name = "YouTube内容采集"
                        elif platform.lower() == "tiktok":
                            workflow_name = "TikTok内容采集"
                        else:
                            workflow_name = f"{platform}内容采集" if platform else "内容采集"
                    # 🔧 新增：为下载任务生成工作流名称
                    elif task_type == "benchmark_download":
                        platform = task.get("platform", "")
                        if platform.lower() == "douyin":
                            workflow_name = "抖音内容下载"
                        elif platform.lower() == "youtube":
                            workflow_name = "YouTube内容下载"
                        elif platform.lower() == "tiktok":
                            workflow_name = "TikTok内容下载"
                        else:
                            workflow_name = f"{platform}内容下载" if platform else "对标账号内容下载"
                    elif task_type == "benchmark_download_batch":
                        workflow_name = "批量内容下载"
                    else:
                        # 原有的YouTube上传任务逻辑
                        metadata = task.get("metadata", {})
                        if isinstance(metadata, dict):
                            content_type = metadata.get("contentType", content_type)

                        # 根据content_type生成workflow_name
                        if content_type == "shorts":
                            workflow_name = "YouTube短视频上传"
                        elif content_type == "video":
                            workflow_name = "YouTube视频上传"
                        elif content_type == "live":
                            workflow_name = "YouTube直播"
                        elif content_type == "post":
                            workflow_name = "YouTube社区发布"
                        elif content_type:
                            workflow_name = f"YouTube{content_type}上传"

                # 🔧 安全修复：下载任务使用download_path字段，不影响现有任务
                content_path = task.get("content_path", "")
                if task_type == "benchmark_download" or task_type == "benchmark_download_batch":
                    content_path = task.get("download_path", content_path)  # 如果没有download_path，保持原值

                formatted_task = {
                    "id": task.get("task_id", task.get("id", "")),
                    "platform_id": platform_id,
                    "platform_name": platform_name,
                    "account_id": account_id,
                    "account_name": account_name,
                    "device_id": task.get("device_id", ""),
                    "content_path": content_path,
                    "status": task.get("status", "unknown"),
                    "progress": task.get("progress", 0),
                    "task_type": task.get("task_type", "single"),
                    "created_at": task.get("created_at", ""),
                    "updated_at": task.get("updated_at", ""),
                    "start_time": task.get("start_time", ""),
                    "end_time": task.get("end_time", ""),  # 🔧 添加缺失的end_time字段
                    "workflow_id": task.get("workflow_id", ""),
                    "workflow_name": workflow_name,  # 动态生成的工作流名称
                    "content_type": content_type,    # 内容类型
                    "video_file": task.get("video_file", "")         # 新增视频文件
                }

                # 如果是主任务，添加子任务信息
                if task.get("task_type") == "main":
                    formatted_task["total_subtasks"] = task.get("total_subtasks", 0)
                    formatted_task["completed_subtasks"] = task.get("completed_subtasks", 0)

                # 如果是子任务，添加父任务信息和视频文件
                if task.get("task_type") == "subtask":
                    formatted_task["parent_task_id"] = task.get("parent_task_id", "")
                    formatted_task["subtask_index"] = task.get("subtask_index", 1)

                    # 子任务继承父任务的工作流信息
                    if not workflow_name and task.get("parent_task_id"):
                        try:
                            parent_task = await db_service.db.social_tasks.find_one({"task_id": task.get("parent_task_id")})
                            if parent_task:
                                parent_metadata = parent_task.get("metadata", {})
                                if isinstance(parent_metadata, dict):
                                    parent_content_type = parent_metadata.get("contentType", "")
                                    if parent_content_type == "shorts":
                                        formatted_task["workflow_name"] = "YouTube短视频上传"
                                    elif parent_content_type == "video":
                                        formatted_task["workflow_name"] = "YouTube视频上传"
                                    elif parent_content_type == "live":
                                        formatted_task["workflow_name"] = "YouTube直播"
                                    elif parent_content_type == "post":
                                        formatted_task["workflow_name"] = "YouTube社区发布"
                                    formatted_task["content_type"] = parent_content_type
                        except Exception as e:
                            logger.warning(f"获取父任务工作流信息失败: {e}")

                # 如果是主任务，添加子任务信息
                if task.get("task_type") == "main":
                    formatted_task["total_subtasks"] = task.get("total_subtasks", 0)
                    formatted_task["completed_subtasks"] = task.get("completed_subtasks", 0)

                # 如果是子任务，添加父任务信息
                if task.get("task_type") == "subtask":
                    formatted_task["parent_task_id"] = task.get("parent_task_id", "")
                    formatted_task["subtask_index"] = task.get("subtask_index", 1)

                # 如果任务失败，获取具体的失败信息
                if task.get("status") == "failed":
                    try:
                        failure_info = await get_task_failure_info(db_service, task.get("task_id"))
                        formatted_task.update(failure_info)
                    except Exception as e:
                        logger.warning(f"获取任务{task.get('task_id')}失败信息异常: {str(e)}")

                formatted_tasks.append(formatted_task)

            logger.info(f"找到 {len(formatted_tasks)} 个任务，数据库总数: {total_count}")

            return {
                "tasks": formatted_tasks,
                "total": len(formatted_tasks),
                "database_total": total_count
            }

        except Exception as e:
            logger.error(f"获取活跃任务失败: {str(e)}", exc_info=True)
            return {
                "tasks": [],
                "total": 0,
                "error": f"获取活跃任务失败: {str(e)}"
            }

    @router.post("/{task_id}/start")
    async def start_task(task_id: str, request: Request):
        """启动任务"""
        try:
            logger.info(f"🚀 收到启动任务请求: {task_id}")
            logger.info(f"请求方法: {request.method}")
            logger.info(f"请求URL: {request.url}")
            logger.info(f"请求头: {dict(request.headers)}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = await db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {"success": False, "error": "任务不存在"}

            # 检查任务状态
            if task.get("status") not in ["pending", "paused", "failed", "canceled"]:
                return {"success": False, "error": f"任务状态为{task.get('status')}，无法启动"}

            # 记录重启信息
            if task.get("status") in ["failed", "canceled"]:
                logger.info(f"重启{task.get('status')}的任务: {task_id}")

            # 检查是否是主任务
            if task.get("task_type") == "main":
                logger.info(f"检测到主任务 {task_id}，重启所有子任务")

                # 查找所有子任务
                cursor = db_service.db.social_tasks.find({
                    "parent_task_id": task_id,
                    "task_type": "subtask"
                }).sort("subtask_index", 1)
                all_subtasks = await cursor.to_list(length=None)

                if not all_subtasks:
                    return {"success": False, "error": "没有找到子任务"}

                logger.info(f"找到 {len(all_subtasks)} 个子任务，准备重置状态")

                # 重置所有子任务状态为pending（除了已完成的）
                reset_count = 0
                for subtask in all_subtasks:
                    subtask_id = subtask["task_id"]
                    current_status = subtask.get("status")

                    # 只重置失败、取消、暂停的子任务，保留已完成的
                    if current_status in ["failed", "canceled", "paused", "running"]:
                        await db_service.db.social_tasks.update_one(
                            {"task_id": subtask_id},
                            {"$set": {
                                "status": "pending",
                                "start_time": None,
                                "end_time": None,
                                "progress": 0,
                                "updated_at": datetime.datetime.now()
                            }}
                        )
                        logger.info(f"重置子任务 {subtask_id} 状态: {current_status} -> pending")
                        reset_count += 1
                    else:
                        logger.info(f"保持子任务 {subtask_id} 状态: {current_status}")

                logger.info(f"重置了 {reset_count} 个子任务状态")

                # 更新主任务状态为运行中（重要：不要设置为completed！）
                await db_service.db.social_tasks.update_one(
                    {"task_id": task_id},
                    {"$set": {
                        "status": "running",  # 主任务应该是running状态，不是completed
                        "start_time": datetime.datetime.now().isoformat(),
                        "end_time": None,  # 清除结束时间
                        "updated_at": datetime.datetime.now(),
                        "completed_subtasks": 0,  # 重置完成计数
                        "progress": 0  # 重置进度
                    }}
                )

                # 找到第一个pending状态的子任务并通过Redis通知启动
                first_pending_subtask = None
                for subtask in all_subtasks:
                    # 重新查询子任务状态（因为可能被重置了）
                    updated_subtask = await db_service.db.social_tasks.find_one({"task_id": subtask["task_id"]})
                    if updated_subtask and updated_subtask.get("status") == "pending":
                        first_pending_subtask = updated_subtask
                        break

                if first_pending_subtask:
                    first_subtask_id = first_pending_subtask["task_id"]
                    logger.info(f"启动第一个待执行的子任务: {first_subtask_id}")

                    # 🔧 改为异步非阻塞模式：立即返回，后台处理任务启动
                    logger.info(f"启动第一个待执行的子任务: {first_subtask_id}")

                    # 创建后台任务来处理Core服务调用，避免阻塞前端
                    asyncio.create_task(
                        start_core_task_async(first_subtask_id, first_pending_subtask, db_service, task_id)
                    )

                return {
                    "success": True,
                    "message": f"主任务重启成功，重置了{reset_count}个子任务，第一个子任务正在后台启动",
                    "details": {
                        "reset_subtasks": reset_count,
                        "first_subtask_id": first_pending_subtask["task_id"] if first_pending_subtask else None,
                        "async_processing": True  # 标识这是异步处理
                    }
                }

            # 如果是子任务或单任务，根据任务类型选择处理方式
            else:
                task_type = task.get('task_type', 'single')
                logger.info(f"启动{task_type}任务: {task_id}")

                # 🔧 重要修复：所有任务都通过Core服务处理，包括下载任务
                logger.info(f"检测到{task_type}任务，使用Core服务处理: {task_id}")

                # 更新任务状态为running
                await db_service.db.social_tasks.update_one(
                    {"task_id": task_id},
                    {"$set": {
                        "status": "running",
                        "start_time": datetime.datetime.now().isoformat(),
                        "updated_at": datetime.datetime.now()
                    }}
                )

                # 🔧 改为异步非阻塞模式：立即返回，后台处理任务启动
                logger.info(f"异步启动{task_type}任务: {task_id}")

                # 准备任务数据，包含特殊字段
                enhanced_task_data = dict(task)  # 复制原始任务数据

                # 🔧 重要修复：为采集任务添加特有字段
                if task.get("task_type") == "collect":
                    enhanced_task_data.update({
                        "platform": task.get("platform", ""),
                        "account_url": task.get("account_url", ""),
                        "account_name": task.get("account_name", ""),
                        "task_config": task.get("task_config", {}),
                        "task_name": task.get("task_name", "")
                    })
                    logger.info(f"为采集任务添加特有字段: platform={task.get('platform')}, account_url={task.get('account_url')}")

                # 🔧 重要修复：为下载任务添加特有字段
                elif task.get("task_type") in ["benchmark_download", "benchmark_download_batch"]:
                    enhanced_task_data.update({
                        "platform": task.get("platform", ""),
                        "benchmark_account_url": task.get("benchmark_account_url", ""),
                        "benchmark_account_name": task.get("benchmark_account_name", ""),
                        "download_path": task.get("download_path", ""),
                        "download_config": task.get("download_config", {}),
                        "our_account_id": task.get("our_account_id", ""),
                        "our_account_name": task.get("our_account_name", ""),
                        "benchmark_account_id": task.get("benchmark_account_id", "")
                    })
                    logger.info(f"为下载任务添加特有字段: platform={task.get('platform')}, account_url={task.get('benchmark_account_url')}")

                # 创建后台任务来处理Core服务调用，避免阻塞前端
                parent_task_id = task.get("parent_task_id") if task.get("task_type") == "subtask" else None
                background_task = asyncio.create_task(
                    start_core_task_async(task_id, enhanced_task_data, db_service, parent_task_id)
                )
                # 使用任务管理器管理后台任务，防止泄露
                background_task_manager.add_task(background_task)

                return {"success": True, "message": "任务启动请求已提交，正在后台处理"}

        except Exception as e:
            logger.error(f"启动任务失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"启动任务失败: {str(e)}"}

    @router.post("/{task_id}/pause")
    async def pause_task(task_id: str, request: Request):
        """暂停任务"""
        try:
            logger.info(f"暂停任务: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = await db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {"success": False, "error": "任务不存在"}

            # 检查任务状态
            if task.get("status") != "running":
                return {"success": False, "error": f"任务状态为{task.get('status')}，无法暂停"}

            # 更新任务状态
            await db_service.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": {"status": "paused", "updated_at": datetime.datetime.now()}}
            )

            # 这里应该通知Core服务暂停任务
            # 暂时只更新数据库状态

            logger.info(f"任务 {task_id} 暂停成功")
            return {"success": True, "message": "任务暂停成功"}

        except Exception as e:
            logger.error(f"暂停任务失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"暂停任务失败: {str(e)}"}

    @router.post("/{task_id}/cancel")
    async def cancel_task(task_id: str, request: Request):
        """取消任务"""
        try:
            logger.info(f"取消任务: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = await db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {"success": False, "error": "任务不存在"}

            # 检查任务状态
            if task.get("status") not in ["pending", "running", "paused"]:
                return {"success": False, "error": f"任务状态为{task.get('status')}，无法取消"}

            # 先尝试通知Core服务取消任务
            core_cancel_success = False
            try:
                from app.core.service_discovery import get_core_client
                core_client = get_core_client()

                logger.info(f"通知Core服务取消任务: {task_id}")
                cancel_result = await core_client.cancel_task(task_id)
                core_cancel_success = cancel_result.get("success", False)

                if core_cancel_success:
                    logger.info(f"Core服务任务 {task_id} 取消成功")
                else:
                    logger.warning(f"Core服务任务 {task_id} 取消失败: {cancel_result.get('error', '未知错误')}")
            except Exception as e:
                logger.error(f"通知Core服务取消任务失败: {str(e)}")

            # 更新数据库中的任务状态
            await db_service.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": {
                    "status": "canceled",
                    "end_time": datetime.datetime.now().isoformat(),
                    "updated_at": datetime.datetime.now(),
                    "core_canceled": core_cancel_success  # 记录Core服务是否成功取消
                }}
            )

            message = "任务取消成功"
            if not core_cancel_success:
                message += "（数据库状态已更新，但Core服务可能仍在运行）"

            logger.info(f"任务 {task_id} 取消完成")
            return {"success": True, "message": message}

        except Exception as e:
            logger.error(f"取消任务失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"取消任务失败: {str(e)}"}

    @router.delete("/{task_id}")
    async def delete_task(task_id: str, request: Request):
        """删除任务"""
        try:
            logger.info(f"删除任务: {task_id}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = await db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {"success": False, "error": "任务不存在"}

            # 如果是主任务，需要特殊处理
            if task.get("task_type") == "main":
                logger.info(f"准备删除主任务 {task_id}，检查子任务状态")

                # 查找所有子任务
                cursor = db_service.db.social_tasks.find({"parent_task_id": task_id})
                subtasks = await cursor.to_list(length=None)
                logger.info(f"找到 {len(subtasks)} 个子任务")

                # 检查是否有活跃的子任务
                active_subtasks = [st for st in subtasks if st.get("status") in ["running", "pending", "paused"]]

                if active_subtasks:
                    active_ids = [st.get("task_id") for st in active_subtasks]
                    logger.warning(f"主任务 {task_id} 有 {len(active_subtasks)} 个活跃子任务: {active_ids}")
                    return {
                        "success": False,
                        "error": f"无法删除主任务，还有 {len(active_subtasks)} 个子任务正在运行或等待中。请先停止或完成所有子任务。",
                        "active_subtasks": active_ids
                    }

                # 检查主任务状态，只能删除已完成、失败或取消的任务
                if task.get("status") not in ["completed", "failed", "canceled"]:
                    return {"success": False, "error": f"主任务状态为{task.get('status')}，无法删除。只能删除已完成、失败或已取消的任务"}

                # 安全删除：先删除所有子任务，再删除主任务
                if subtasks:
                    subtask_result = await db_service.db.social_tasks.delete_many({"parent_task_id": task_id})
                    logger.info(f"删除了 {subtask_result.deleted_count} 个子任务")
            else:
                # 单任务或子任务的状态检查
                if task.get("status") not in ["completed", "failed", "canceled"]:
                    return {"success": False, "error": f"任务状态为{task.get('status')}，无法删除。只能删除已完成、失败或已取消的任务"}

            # 删除主任务或单任务
            result = await db_service.db.social_tasks.delete_one({"task_id": task_id})

            if result.deleted_count == 0:
                return {"success": False, "error": "删除任务失败"}

            logger.info(f"任务 {task_id} 删除成功")
            return {"success": True, "message": "任务删除成功"}

        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"删除任务失败: {str(e)}"}

    @router.post("/{task_id}/fix")
    async def fix_task_status(task_id: str, request: Request):
        """修复任务状态和进度

        用于修复父子任务状态不一致的问题
        """
        try:
            logger.info(f"修复任务状态: {task_id}")

            # 获取修复数据
            fix_data = await request.json()
            logger.info(f"修复数据: {fix_data}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 检查任务是否存在
            task = await db_service.db.social_tasks.find_one({"task_id": task_id})
            if not task:
                return {"success": False, "error": "任务不存在"}

            # 构建更新数据
            update_data = {
                "updated_at": datetime.datetime.now()
            }

            # 添加需要修复的字段
            for field in ['status', 'progress', 'completed_subtasks', 'end_time', 'start_time']:
                if field in fix_data:
                    update_data[field] = fix_data[field]

            # 执行更新
            result = await db_service.db.social_tasks.update_one(
                {"task_id": task_id},
                {"$set": update_data}
            )

            if result.modified_count > 0:
                logger.info(f"任务 {task_id} 状态修复成功: {update_data}")

                # 如果是主任务，触发状态同步以确保一致性
                if task.get("task_type") == "main":
                    await sync_main_task_status(db_service, task_id)

                return {"success": True, "message": "任务状态修复成功", "updated_fields": list(fix_data.keys())}
            else:
                logger.warning(f"任务 {task_id} 状态未发生变化")
                return {"success": True, "message": "任务状态未发生变化"}

        except Exception as e:
            logger.error(f"修复任务状态失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"修复任务状态失败: {str(e)}"}

    @router.post("/sync-all")
    async def sync_all_main_tasks(request: Request):
        """同步所有主任务状态

        批量修复所有主任务的状态和进度
        """
        try:
            logger.info("开始同步所有主任务状态")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 获取所有主任务
            main_tasks_cursor = db_service.db.social_tasks.find({"task_type": "main"})
            main_tasks = await main_tasks_cursor.to_list(length=None)
            logger.info(f"找到 {len(main_tasks)} 个主任务")

            synced_count = 0
            error_count = 0

            for main_task in main_tasks:
                main_task_id = main_task.get("task_id")
                try:
                    await sync_main_task_status(db_service, main_task_id)
                    synced_count += 1
                except Exception as e:
                    logger.error(f"同步主任务 {main_task_id} 失败: {str(e)}")
                    error_count += 1

            logger.info(f"主任务状态同步完成: 成功 {synced_count} 个, 失败 {error_count} 个")

            return {
                "success": True,
                "message": f"主任务状态同步完成",
                "synced_count": synced_count,
                "error_count": error_count,
                "total_count": len(main_tasks)
            }

        except Exception as e:
            logger.error(f"批量同步主任务状态失败: {str(e)}", exc_info=True)
            return {"success": False, "error": f"批量同步失败: {str(e)}"}

    @router.get("/history")
    async def get_task_history(
        request: Request,
        status: Optional[str] = Query(None, description="任务状态过滤"),
        platform_id: Optional[str] = Query(None, description="平台ID过滤"),
        start_date: Optional[str] = Query(None, description="开始日期"),
        end_date: Optional[str] = Query(None, description="结束日期"),
        limit: int = Query(200, description="返回数量限制"),
        offset: int = Query(0, description="偏移量")
    ):
        """获取任务历史"""
        try:
            logger.info(f"获取任务历史: status={status}, platform_id={platform_id}, limit={limit}, offset={offset}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 🔧 完全参照任务管理接口的查询逻辑
            # 构建查询条件 - 默认查询所有状态的任务
            query = {
                "status": {"$in": ["running", "pending", "paused", "failed", "canceled", "completed"]}
            }

            # 只有当明确指定了状态时才覆盖默认状态过滤
            if status and status.strip():
                query["status"] = status
                logger.info(f"按状态过滤: {status}")

            # 只有当明确指定了平台时才过滤
            if platform_id and platform_id.strip():
                query["platform_id"] = platform_id
                logger.info(f"按平台过滤: {platform_id}")
            if start_date and start_date.strip():
                # 注意：数据库中的created_at是数字时间戳，需要转换
                try:
                    import time
                    start_timestamp = time.mktime(time.strptime(start_date, "%Y-%m-%d"))
                    query["created_at"] = {"$gte": start_timestamp}
                    logger.info(f"按开始日期过滤: {start_date} (时间戳: {start_timestamp})")
                except ValueError:
                    logger.warning(f"开始日期格式错误: {start_date}")
            if end_date and end_date.strip():
                try:
                    import time
                    end_timestamp = time.mktime(time.strptime(end_date + " 23:59:59", "%Y-%m-%d %H:%M:%S"))
                    if "created_at" in query:
                        query["created_at"]["$lte"] = end_timestamp
                    else:
                        query["created_at"] = {"$lte": end_timestamp}
                    logger.info(f"按结束日期过滤: {end_date} (时间戳: {end_timestamp})")
                except ValueError:
                    logger.warning(f"结束日期格式错误: {end_date}")

            logger.info(f"最终查询条件: {query}")

            # 先统计总数
            total_count = await db_service.db.social_tasks.count_documents(query)
            logger.info(f"符合条件的任务总数: {total_count}")

            # 从数据库获取任务历史 - 直接参照任务管理接口的做法
            tasks_cursor = db_service.db.social_tasks.find(query).sort("_id", -1).skip(offset).limit(limit)
            tasks = await tasks_cursor.to_list(length=limit)
            logger.info(f"实际查询到的任务数量: {len(tasks)}")

            # 格式化任务数据
            formatted_tasks = []
            for task in tasks:
                # 移除MongoDB的_id字段
                if '_id' in task:
                    del task['_id']

                # 🔧 重要：先获取task_type
                task_type = task.get("task_type", "")

                # 获取平台信息
                platform_name = "未知平台"
                platform_id = task.get("platform_id", "")

                # 🔧 安全修复：为采集任务和下载任务特殊处理，不影响现有任务
                if task_type == "collect":
                    # 采集任务使用platform字段
                    platform_name = task.get("platform", "未知平台")
                    if platform_name.lower() == "douyin":
                        platform_name = "抖音"
                    elif platform_name.lower() == "youtube":
                        platform_name = "YouTube"
                    elif platform_name.lower() == "tiktok":
                        platform_name = "TikTok"
                elif task_type == "benchmark_download" or task_type == "benchmark_download_batch":
                    # 下载任务使用platform字段
                    platform_name = task.get("platform", "未知平台")
                    if platform_name.lower() == "douyin":
                        platform_name = "抖音"
                    elif platform_name.lower() == "youtube":
                        platform_name = "YouTube"
                    elif platform_name.lower() == "tiktok":
                        platform_name = "TikTok"

                # 原有逻辑保持不变
                if platform_id:
                    try:
                        # 尝试使用ObjectId查询
                        if isinstance(platform_id, str) and len(platform_id) == 24:
                            platform = await db_service.db.social_platforms.find_one({"_id": ObjectId(platform_id)})
                        else:
                            platform = await db_service.db.social_platforms.find_one({"_id": platform_id})

                        if platform:
                            platform_name = platform.get("name", platform.get("id", "未知平台"))
                            logger.debug(f"找到平台: {platform_name}")
                        else:
                            logger.warning(f"未找到平台: {platform_id}")
                    except Exception as e:
                        logger.warning(f"获取平台信息失败: {e}")

                # 获取账号信息
                account_name = "未知账号"
                account_id = task.get("account_id", "")

                # 🔧 安全修复：为采集任务和下载任务特殊处理，不影响现有任务
                if task_type == "collect":
                    # 采集任务使用account_name字段
                    account_name = task.get("account_name", "未知账号")
                elif task_type == "benchmark_download" or task_type == "benchmark_download_batch":
                    # 下载任务使用our_account_name字段
                    account_name = task.get("our_account_name", "未知账号")
                    account_id = task.get("our_account_id", "")

                # 原有逻辑保持不变
                if account_id and (task_type not in ["collect", "benchmark_download", "benchmark_download_batch"]):
                    try:
                        # 尝试使用ObjectId查询
                        if isinstance(account_id, str) and len(account_id) == 24:
                            account = await db_service.db.social_accounts.find_one({"_id": ObjectId(account_id)})
                        else:
                            account = await db_service.db.social_accounts.find_one({"_id": account_id})

                        if account:
                            account_name = account.get("display_name", account.get("username", "未知账号"))
                            logger.debug(f"找到账号: {account_name}")
                        else:
                            logger.warning(f"未找到账号: {account_id}")
                    except Exception as e:
                        logger.warning(f"获取账号信息失败: {e}")

                # 动态生成工作流名称（兼容旧数据）
                workflow_name = task.get("workflow_name", "")
                content_type = task.get("content_type", "")

                # 如果没有workflow_name，根据任务类型生成
                if not workflow_name:
                    # 🔧 新增：为采集任务生成工作流名称
                    if task_type == "collect":
                        platform = task.get("platform", "")
                        if platform.lower() == "douyin":
                            workflow_name = "抖音内容采集"
                        elif platform.lower() == "youtube":
                            workflow_name = "YouTube内容采集"
                        elif platform.lower() == "tiktok":
                            workflow_name = "TikTok内容采集"
                        else:
                            workflow_name = f"{platform}内容采集" if platform else "内容采集"
                    # 🔧 新增：为下载任务生成工作流名称
                    elif task_type == "benchmark_download":
                        platform = task.get("platform", "")
                        if platform.lower() == "douyin":
                            workflow_name = "抖音内容下载"
                        elif platform.lower() == "youtube":
                            workflow_name = "YouTube内容下载"
                        elif platform.lower() == "tiktok":
                            workflow_name = "TikTok内容下载"
                        else:
                            workflow_name = f"{platform}内容下载" if platform else "对标账号内容下载"
                    elif task_type == "benchmark_download_batch":
                        workflow_name = "批量内容下载"
                    else:
                        # 原有的YouTube上传任务逻辑
                        metadata = task.get("metadata", {})
                        if isinstance(metadata, dict):
                            content_type = metadata.get("contentType", content_type)

                        # 根据content_type生成workflow_name
                        if content_type == "shorts":
                            workflow_name = "YouTube短视频上传"
                        elif content_type == "video":
                            workflow_name = "YouTube视频上传"
                        elif content_type == "live":
                            workflow_name = "YouTube直播"
                        elif content_type == "post":
                            workflow_name = "YouTube社区发布"
                        elif content_type:
                            workflow_name = f"YouTube{content_type}上传"

                # 获取失败原因信息
                failure_reason = ""
                failure_details = ""

                if task.get("status") == "failed":
                    # 从error_info中获取失败信息
                    error_info = task.get("error_info", {})
                    if error_info:
                        error_type = error_info.get("error_type", "")
                        error_message = error_info.get("error_message", "")

                        # 构建简短的失败原因
                        if "ElementNotFound" in error_type or "element" in error_message.lower():
                            failure_reason = "界面元素定位失败"
                        elif "Timeout" in error_type or "timeout" in error_message.lower():
                            failure_reason = "操作超时"
                        elif "Connection" in error_type or "connection" in error_message.lower():
                            failure_reason = "设备连接失败"
                        elif "Permission" in error_type or "permission" in error_message.lower():
                            failure_reason = "权限不足"
                        else:
                            failure_reason = error_message[:50] + "..." if len(error_message) > 50 else error_message

                        # 构建详细的失败信息
                        failure_details = f"{error_type}: {error_message}"
                        if error_info.get("device_id"):
                            failure_details += f" (设备: {error_info.get('device_id')})"
                    else:
                        # 根据进度推断失败原因
                        progress = task.get("progress", 0)
                        if progress < 20:
                            failure_reason = "设备连接失败"
                            failure_details = "任务在设备连接阶段失败，请检查设备状态和Appium服务"
                        elif progress < 40:
                            failure_reason = "应用启动失败"
                            failure_details = f"{platform_name}应用启动失败，请检查应用是否已安装"
                        elif progress < 60:
                            failure_reason = "内容处理失败"
                            failure_details = "视频文件处理或界面操作失败，请检查文件格式和应用界面"
                        elif progress < 80:
                            failure_reason = "上传过程失败"
                            failure_details = f"上传到{platform_name}失败，请检查网络连接"
                        else:
                            failure_reason = "任务完成失败"
                            failure_details = "任务在最后阶段失败，请查看详细日志"

                # 🔧 安全修复：下载任务使用download_path字段，不影响现有任务
                content_path = task.get("content_path", "")
                if task_type == "benchmark_download" or task_type == "benchmark_download_batch":
                    content_path = task.get("download_path", content_path)  # 如果没有download_path，保持原值

                # 确保必要字段存在
                formatted_task = {
                    "id": task.get("task_id", task.get("id", "")),  # 优先使用task_id
                    "platform_id": platform_id,
                    "platform_name": platform_name,  # 新增平台名称
                    "account_id": account_id,
                    "account_name": account_name,  # 新增账号名称
                    "device_id": task.get("device_id", ""),
                    "content_path": content_path,
                    "status": task.get("status", "unknown"),
                    "progress": task.get("progress", 100 if task.get("status") == "completed" else 0),  # 已完成任务进度为100%
                    "created_at": task.get("created_at", ""),
                    "updated_at": task.get("updated_at", ""),
                    "start_time": task.get("start_time", ""),
                    "end_time": task.get("end_time", ""),
                    "estimated_end_time": task.get("estimated_end_time", ""),
                    "workflow_id": task.get("workflow_id", ""),
                    "workflow_name": workflow_name,  # 动态生成的工作流名称
                    "content_type": content_type,    # 内容类型
                    "task_type": task.get("task_type", "single"),  # 添加任务类型
                    "params": task.get("params", {}),
                    # 失败信息
                    "failure_reason": failure_reason,
                    "failure_details": failure_details,
                    "video_file": task.get("video_file", "")  # 新增视频文件
                }

                # 如果是主任务，添加子任务信息
                if task.get("task_type") == "main":
                    formatted_task["total_subtasks"] = task.get("total_subtasks", 0)
                    formatted_task["completed_subtasks"] = task.get("completed_subtasks", 0)

                # 如果是子任务，添加父任务信息和视频文件
                if task.get("task_type") == "subtask":
                    formatted_task["parent_task_id"] = task.get("parent_task_id", "")
                    formatted_task["subtask_index"] = task.get("subtask_index", 1)
                    formatted_task["video_file"] = task.get("video_file", "")

                    # 子任务继承父任务的工作流信息
                    if not workflow_name and task.get("parent_task_id"):
                        try:
                            parent_task = await db_service.db.social_tasks.find_one({"task_id": task.get("parent_task_id")})
                            if parent_task:
                                parent_metadata = parent_task.get("metadata", {})
                                if isinstance(parent_metadata, dict):
                                    parent_content_type = parent_metadata.get("contentType", "")
                                    if parent_content_type == "shorts":
                                        formatted_task["workflow_name"] = "YouTube短视频上传"
                                    elif parent_content_type == "video":
                                        formatted_task["workflow_name"] = "YouTube视频上传"
                                    elif parent_content_type == "live":
                                        formatted_task["workflow_name"] = "YouTube直播"
                                    elif parent_content_type == "post":
                                        formatted_task["workflow_name"] = "YouTube社区发布"
                                    formatted_task["content_type"] = parent_content_type
                        except Exception as e:
                            logger.warning(f"获取父任务工作流信息失败: {e}")
                formatted_tasks.append(formatted_task)

            logger.info(f"找到 {len(formatted_tasks)} 条历史记录，总计 {total_count} 条")

            return {
                "tasks": formatted_tasks,
                "total": total_count,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "has_more": total_count > offset + len(formatted_tasks)
                }
            }

        except Exception as e:
            logger.error(f"获取任务历史失败: {str(e)}", exc_info=True)
            return {
                "tasks": [],
                "total": 0,
                "error": f"获取任务历史失败: {str(e)}"
            }

    @router.post("/history/clean")
    async def clean_task_history(request: Request):
        """清理任务历史"""
        try:
            # 获取请求数据
            clean_data = await request.json()
            logger.info(f"清理任务历史: {clean_data}")

            # 获取数据库连接
            db = request.app.state.mongo_db
            db_service = SocialDatabaseService(db)

            # 构建删除条件
            delete_query = {}

            # 按日期清理
            if "before_date" in clean_data and clean_data["before_date"]:
                before_date = clean_data["before_date"]
                # 将日期字符串转换为datetime对象
                try:
                    import datetime as dt
                    date_obj = dt.datetime.strptime(before_date, "%Y-%m-%d")
                    delete_query["created_at"] = {"$lt": date_obj}
                    logger.info(f"按日期清理: 删除 {before_date} 之前的任务")
                except ValueError as e:
                    logger.error(f"日期格式错误: {before_date}, {e}")
                    return {"success": False, "error": "日期格式错误"}

            # 按状态清理（智能级联删除，防止孤儿任务）
            elif "status" in clean_data and clean_data["status"]:
                status = clean_data["status"]
                logger.info(f"按状态清理: 删除状态为 {status} 的任务")

                # 🔧 智能级联删除逻辑：
                # 1. 找到所有指定状态的任务
                # 2. 如果是主任务，同时删除所有子任务（无论子任务状态）
                # 3. 如果是子任务，检查是否会产生孤儿主任务

                # 找到所有指定状态的任务
                cursor = db_service.db.social_tasks.find({"status": status})
                tasks_with_status = await cursor.to_list(length=None)
                logger.info(f"找到 {len(tasks_with_status)} 个状态为 {status} 的任务")

                task_ids_to_delete = []

                for task in tasks_with_status:
                    task_id = task.get("task_id")
                    task_type = task.get("task_type")

                    if not task_id:
                        continue

                    task_ids_to_delete.append(task_id)

                    # 如果是主任务，必须删除所有子任务（防止孤儿子任务）
                    if task_type == "main":
                        cursor = db_service.db.social_tasks.find({"parent_task_id": task_id})
                        subtasks = await cursor.to_list(length=None)
                        for subtask in subtasks:
                            subtask_id = subtask.get("task_id")
                            if subtask_id and subtask_id not in task_ids_to_delete:
                                task_ids_to_delete.append(subtask_id)

                        logger.info(f"主任务 {task_id} 包含 {len(subtasks)} 个子任务，将一并删除")

                    # 如果是子任务，检查主任务状态
                    elif task_type == "subtask":
                        parent_task_id = task.get("parent_task_id")
                        if parent_task_id:
                            # 检查主任务是否也要被删除
                            parent_task = await db_service.db.social_tasks.find_one({"task_id": parent_task_id})
                            if parent_task and parent_task.get("status") == status:
                                # 主任务也会被删除，不需要特殊处理
                                pass
                            else:
                                # 主任务不会被删除，检查是否会成为孤儿主任务
                                cursor = db_service.db.social_tasks.find({
                                    "parent_task_id": parent_task_id,
                                    "task_id": {"$ne": task_id}  # 排除当前要删除的子任务
                                })
                                remaining_subtasks = await cursor.to_list(length=None)

                                if not remaining_subtasks:
                                    # 删除这个子任务后，主任务将没有子任务，也删除主任务
                                    if parent_task_id not in task_ids_to_delete:
                                        task_ids_to_delete.append(parent_task_id)
                                    logger.info(f"子任务 {task_id} 删除后，主任务 {parent_task_id} 将成为孤儿，一并删除")

                if task_ids_to_delete:
                    delete_query = {"task_id": {"$in": task_ids_to_delete}}
                    logger.info(f"智能级联删除: 将删除 {len(task_ids_to_delete)} 个任务")
                    logger.info(f"删除的任务ID: {task_ids_to_delete}")
                else:
                    logger.info("没有找到需要删除的任务")
                    return {"success": True, "deleted_count": 0, "message": "没有找到需要删除的任务"}

            # 按保留数量清理（智能保留，维护任务关系）
            elif "keep_count" in clean_data and clean_data["keep_count"]:
                keep_count = int(clean_data["keep_count"])
                logger.info(f"按保留数量清理: 保留最新 {keep_count} 个任务组")

                # 智能任务保留逻辑：
                # 1. 主任务和单任务按创建时间排序
                # 2. 保留最新的N个任务组（主任务+子任务算作一组，单任务算作一组）
                # 3. 确保不会出现孤儿子任务

                # 获取所有主任务和单任务，按创建时间倒序排列
                cursor = db_service.db.social_tasks.find({
                    "$or": [
                        {"task_type": "main"},
                        {"task_type": "single"},
                        {"task_type": {"$exists": False}}  # 兼容没有task_type的旧任务
                    ]
                }).sort("created_at", -1)
                main_and_single_tasks = await cursor.to_list(length=None)

                logger.info(f"找到 {len(main_and_single_tasks)} 个任务组（主任务+单任务）")

                if len(main_and_single_tasks) > keep_count:
                    # 需要删除的任务组
                    tasks_to_delete = main_and_single_tasks[keep_count:]

                    # 收集要删除的任务ID
                    task_ids_to_delete = []

                    for task in tasks_to_delete:
                        task_id = task.get("task_id")
                        if task_id:
                            task_ids_to_delete.append(task_id)

                            # 如果是主任务，还要删除所有子任务
                            if task.get("task_type") == "main":
                                cursor = db_service.db.social_tasks.find({"parent_task_id": task_id})
                                subtasks = await cursor.to_list(length=None)
                                for subtask in subtasks:
                                    subtask_id = subtask.get("task_id")
                                    if subtask_id:
                                        task_ids_to_delete.append(subtask_id)
                                logger.info(f"主任务 {task_id} 包含 {len(subtasks)} 个子任务，将一并删除")

                    if task_ids_to_delete:
                        delete_query = {"task_id": {"$in": task_ids_to_delete}}
                        logger.info(f"将删除 {len(task_ids_to_delete)} 个任务（包括子任务）")
                    else:
                        logger.info("没有需要删除的任务")
                        return {"success": True, "deleted_count": 0, "message": "没有需要删除的任务"}
                else:
                    logger.info(f"当前任务组数量 {len(main_and_single_tasks)} 不超过保留数量 {keep_count}")
                    return {"success": True, "deleted_count": 0, "message": "当前任务组数量不超过保留数量"}

            # 清理无效任务（没有task_id的记录和孤儿子任务）
            elif "clean_invalid" in clean_data and clean_data["clean_invalid"]:
                logger.info("清理无效任务: 删除没有task_id的记录和孤儿子任务")

                # 首先找出所有孤儿子任务
                pipeline = [
                    {"$match": {"task_type": "subtask", "parent_task_id": {"$exists": True}}},
                    {"$lookup": {
                        "from": "social_tasks",
                        "localField": "parent_task_id",
                        "foreignField": "task_id",
                        "as": "parent"
                    }},
                    {"$match": {"parent": {"$size": 0}}},
                    {"$project": {"task_id": 1}}
                ]

                cursor = db_service.db.social_tasks.aggregate(pipeline)
                orphan_subtasks = await cursor.to_list(length=None)
                orphan_task_ids = [task["task_id"] for task in orphan_subtasks]

                logger.info(f"找到 {len(orphan_task_ids)} 个孤儿子任务: {orphan_task_ids}")

                # 构建删除查询：无效任务 + 孤儿子任务
                delete_conditions = [
                    {"task_id": {"$exists": False}},
                    {"task_id": None},
                    {"task_id": ""}
                ]

                if orphan_task_ids:
                    delete_conditions.append({"task_id": {"$in": orphan_task_ids}})

                delete_query = {"$or": delete_conditions}

            else:
                return {"success": False, "error": "请指定清理条件"}

            # 执行删除操作
            if delete_query:
                logger.info(f"执行删除操作，查询条件: {delete_query}")
                result = await db_service.db.social_tasks.delete_many(delete_query)
                deleted_count = result.deleted_count

                logger.info(f"清理完成，删除了 {deleted_count} 个任务")
                return {
                    "success": True,
                    "deleted_count": deleted_count,
                    "message": f"成功清理了 {deleted_count} 个历史任务"
                }
            else:
                return {"success": False, "error": "没有匹配的清理条件"}

        except Exception as e:
            logger.error(f"清理任务历史失败: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": f"清理任务历史失败: {str(e)}"
            }

    return router
